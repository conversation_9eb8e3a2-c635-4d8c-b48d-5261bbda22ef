#!/usr/bin/env python3
"""
Simple test script to verify AI orchestrator functionality after environment variable fixes.
"""

import asyncio
import websockets
import json

async def test_ai_processing():
    """Test the complete AI processing pipeline through WebSocket."""
    try:
        uri = 'ws://localhost:8000/ws'
        async with websockets.connect(uri) as websocket:
            print('✅ WebSocket connected successfully!')
            
            # Send connection message first
            connect_message = {
                'type': 'connect',
                'payload': {
                    'clientId': 'test-client-ai-processing'
                }
            }
            await websocket.send(json.dumps(connect_message))
            
            # Read connection response
            response = await websocket.recv()
            print(f'✅ Connection confirmed: {json.loads(response).get("type")}')
            
            # Now send a process_input message to test AI processing
            test_message = {
                'type': 'process_input',
                'content': 'Create a task to review the AI processing pipeline'
            }
            await websocket.send(json.dumps(test_message))
            print('📤 Process input message sent, waiting for AI processing steps...')
            
            # Read multiple responses (AI processing steps)
            step_count = 0
            for i in range(10):  # Expect multiple processing steps
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=20)
                    data = json.loads(response)
                    msg_type = data.get('type', 'unknown')
                    
                    if msg_type == 'processing_step':
                        step_count += 1
                        payload = data.get('payload', {})
                        step = payload.get('step', 'unknown')
                        message = payload.get('message', 'No message')
                        print(f'📋 Step {step_count}: {step} - {message}')
                        
                        # If we got a category identification, that means AI is working
                        if 'identified' in step:
                            category = payload.get('category', 'unknown')
                            confidence = payload.get('confidence', 0)
                            print(f'🎯 AI categorized as: {category} (confidence: {confidence})')
                            
                    elif msg_type == 'processing_complete':
                        print('✅ Processing completed successfully!')
                        break
                        
                    elif msg_type == 'processing_error':
                        error_msg = data.get('payload', {}).get('error', 'Unknown error')
                        print(f'❌ Processing error: {error_msg}')
                        break
                        
                except asyncio.TimeoutError:
                    print(f'⏰ Timeout waiting for response {i+1}')
                    break
                except Exception as e:
                    print(f'❌ Error reading response {i+1}: {e}')
                    break
                    
            if step_count > 0:
                print(f'✅ AI processing pipeline is working! Received {step_count} processing steps.')
            else:
                print('❌ No processing steps received - AI pipeline may still have issues.')
                    
    except Exception as e:
        print(f'❌ WebSocket test failed: {e}')

if __name__ == '__main__':
    asyncio.run(test_ai_processing())
