import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Key, Palette, Search, Cpu, 
  Save, Eye, EyeOff, 
  Check, AlertCircle, Loader
} from 'lucide-react'

// Backend settings data structure
interface SettingsData {
  openrouter_api_key: string
  langsearch_api_key: string
  primary_llm_model: string
  fallback_llm_model: string
  llm_temperature: number
  max_response_tokens: number
  max_search_results: number
  web_search_timeout: number
  theme: string
  notifications_enabled: boolean
  auto_save: boolean
  animation_duration_ms: number
  enable_animations: boolean
  max_workers: number
  cache_ttl: number
  enable_caching: boolean
}

// API response structure
interface SettingsResponse {
  success: boolean
  settings?: SettingsData
  error?: string
  message?: string
}

/**
 * Simple Settings page component to test basic functionality
 */
const Settings: React.FC = () => {
  // Default settings structure
  const [settings, setSettings] = useState<SettingsData>({
    openrouter_api_key: '',
    langsearch_api_key: '',
    primary_llm_model: 'qwen/qwen3-235b-a22b-07-25:free',
    fallback_llm_model: 'microsoft/phi-3.5-mini-instruct:free',
    llm_temperature: 0.7,
    max_response_tokens: 2048,
    max_search_results: 10,
    web_search_timeout: 20,
    theme: 'dark',
    notifications_enabled: true,
    auto_save: true,
    animation_duration_ms: 300,
    enable_animations: true,
    max_workers: 4,
    cache_ttl: 3600,
    enable_caching: true
  })

  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [showApiKey, setShowApiKey] = useState<{[key: string]: boolean}>({
    openrouter: false,
    langsearch: false
  })
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState<string>('')

  // Load settings from backend on component mount
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true)
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const data: SettingsResponse = await response.json()
          if (data.success && data.settings) {
            setSettings(data.settings)
          }
        }
      } catch (error) {
        console.error('Failed to load settings:', error)
        setErrorMessage('Failed to load settings from server')
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [])

  // Save settings to backend
  const saveSettings = async () => {
    setIsSaving(true)
    setSaveStatus('idle')
    setErrorMessage('')

    try {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings }),
      })

      if (response.ok) {
        const data: SettingsResponse = await response.json()
        if (data.success) {
          setSaveStatus('success')
          setTimeout(() => setSaveStatus('idle'), 3000)
        } else {
          throw new Error(data.error || 'Failed to save settings')
        }
      } else {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
    } catch (error) {
      console.error('Failed to save settings:', error)
      setSaveStatus('error')
      setErrorMessage(`Failed to save settings: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  const updateSetting = <K extends keyof SettingsData>(key: K, value: SettingsData[K]) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const toggleApiKeyVisibility = (key: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  if (isLoading) {
    return (
      <div className="p-6 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 min-h-screen bg-gray-50"
    >
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
          <p className="text-gray-600">Configure your AI-powered search experience</p>
        </div>
        
        {/* Save Button */}
        <button
          onClick={saveSettings}
          disabled={isSaving}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
        >
          {isSaving ? (
            <Loader className="w-4 h-4 animate-spin" />
          ) : saveStatus === 'success' ? (
            <Check className="w-4 h-4" />
          ) : saveStatus === 'error' ? (
            <AlertCircle className="w-4 h-4" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          {isSaving ? 'Saving...' : saveStatus === 'success' ? 'Saved!' : saveStatus === 'error' ? 'Error' : 'Save Changes'}
        </button>
      </div>

      {/* Error Message */}
      {errorMessage && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700">
          <AlertCircle className="w-5 h-5 flex-shrink-0" />
          <p>{errorMessage}</p>
        </div>
      )}

      {/* Settings Content */}
      <div className="max-w-4xl mx-auto">
        <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
          {/* API Keys Section */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Key className="w-5 h-5 text-blue-600" />
              API Keys
            </h2>
            
            <div className="space-y-6">
              {/* OpenRouter API Key */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  OpenRouter API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKey.openrouter ? 'text' : 'password'}
                    value={settings.openrouter_api_key}
                    onChange={(e) => updateSetting('openrouter_api_key', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500"
                    placeholder="sk-or-..."
                  />
                  <button
                    type="button"
                    onClick={() => toggleApiKeyVisibility('openrouter')}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showApiKey.openrouter ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Required for AI processing. Get your key from openrouter.ai
                </p>
              </div>

              {/* LangSearch API Key */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  LangSearch API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKey.langsearch ? 'text' : 'password'}
                    value={settings.langsearch_api_key}
                    onChange={(e) => updateSetting('langsearch_api_key', e.target.value)}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500"
                    placeholder="ls-..."
                  />
                  <button
                    type="button"
                    onClick={() => toggleApiKeyVisibility('langsearch')}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showApiKey.langsearch ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Required for web search. Get your key from langsearch.com
                </p>
              </div>
            </div>
          </div>

          {/* AI Models Section */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Cpu className="w-5 h-5 text-purple-600" />
              AI Models
            </h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Primary LLM Model
                </label>
                <select
                  value={settings.primary_llm_model}
                  onChange={(e) => updateSetting('primary_llm_model', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500/30 focus:border-purple-500"
                >
                  <option value="qwen/qwen3-235b-a22b-07-25:free">Qwen3 235B (Free)</option>
                  <option value="tngtech/deepseek-r1t2-chimera:free">DeepSeek R1T2 Chimera (Free)</option>
                  <option value="microsoft/phi-3.5-mini-instruct:free">Phi-3.5 Mini (Free)</option>
                  <option value="google/gemini-2.0-flash-exp:free">Gemini 2.0 Flash (Free)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Temperature: {settings.llm_temperature}
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={settings.llm_temperature}
                  onChange={(e) => updateSetting('llm_temperature', parseFloat(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Conservative (0)</span>
                  <span>Creative (2)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Search Settings */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Search className="w-5 h-5 text-green-600" />
              Search Settings
            </h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Search Results: {settings.max_search_results}
                </label>
                <input
                  type="range"
                  min="1"
                  max="50"
                  value={settings.max_search_results}
                  onChange={(e) => updateSetting('max_search_results', parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>1</span>
                  <span>50</span>
                </div>
              </div>
            </div>
          </div>

          {/* Appearance */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Palette className="w-5 h-5 text-orange-600" />
              Appearance
            </h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Theme</label>
                <div className="grid grid-cols-3 gap-3">
                  {['light', 'dark', 'auto'].map((theme) => (
                    <button
                      key={theme}
                      onClick={() => updateSetting('theme', theme)}
                      className={`p-3 rounded-lg border-2 transition-colors capitalize ${
                        settings.theme === theme
                          ? 'border-orange-500 bg-orange-50 text-orange-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {theme}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-700">Enable Animations</h4>
                  <p className="text-xs text-gray-500">Visual transitions and effects</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.enable_animations}
                    onChange={(e) => updateSetting('enable_animations', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default Settings
