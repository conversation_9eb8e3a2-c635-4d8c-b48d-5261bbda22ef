import requests

BASE_URL = "http://localhost:3000"
TIMEOUT = 30

def test_ai_orchestrator_input_categorization():
    url = f"{BASE_URL}/api/ai-orchestrator/categorize"
    headers = {
        "Content-Type": "application/json"
    }

    # Arbitrary diverse inputs to test categorization without keyword hardcoding
    test_inputs = [
        "Finish the quarterly report by next Monday",
        "Schedule a meeting with the marketing team on Friday at 3pm",
        "What is the weather forecast for New York tomorrow?",
        "Remind me to call the dentist next week",
        "How do I reset my password?",
        "Plan a birthday party event for <PERSON>",
        "Is the stock market going to rise this week?",
        "Buy groceries after work",
        "When is the next full moon?",
        "Set an alarm for 7am tomorrow"
    ]

    for user_input in test_inputs:
        payload = {"input_text": user_input}
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=TIMEOUT)
        except requests.RequestException as e:
            assert False, f"Request failed for input '{user_input}': {e}"

        assert response.status_code == 200, f"Unexpected status code {response.status_code} for input '{user_input}'"

        try:
            data = response.json()
        except ValueError:
            assert False, f"Response is not valid JSON for input '{user_input}'"

        # Expected response structure:
        # {
        #   "category": "Task" | "Event" | "AI Question",
        #   "confidence": float (0.0 - 1.0),
        #   "reasoning": "string"
        # }

        assert "category" in data, f"'category' missing in response for input '{user_input}'"
        assert data["category"] in ["Task", "Event", "AI Question"], f"Invalid category '{data.get('category')}' for input '{user_input}'"

        assert "confidence" in data, f"'confidence' missing in response for input '{user_input}'"
        confidence = data["confidence"]
        assert isinstance(confidence, (float, int)), f"'confidence' is not a number for input '{user_input}'"
        assert 0.0 <= confidence <= 1.0, f"'confidence' out of range for input '{user_input}'"
        assert confidence > 0.7, f"Confidence {confidence} too low for input '{user_input}'"

        assert "reasoning" in data, f"'reasoning' missing in response for input '{user_input}'"
        reasoning = data["reasoning"]
        assert isinstance(reasoning, str) and len(reasoning) > 0, f"Invalid 'reasoning' for input '{user_input}'"

test_ai_orchestrator_input_categorization()