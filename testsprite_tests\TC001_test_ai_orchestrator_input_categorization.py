import requests

BASE_URL = "http://localhost:3000"
TIMEOUT = 30

def test_ai_orchestrator_input_categorization():
    url = f"{BASE_URL}/api/ai-orchestrator/categorize"
    headers = {
        "Content-Type": "application/json"
    }

    # Arbitrary user inputs to test categorization without keyword hardcoding
    test_inputs = [
        "Schedule a meeting with the design team next Thursday at 3pm",
        "Remind me to buy groceries tomorrow",
        "What is the capital of France?",
        "Finish the quarterly report by Friday",
        "Is it going to rain this weekend?",
        "Plan a birthday party for <PERSON>",
        "How do I reset my password?",
        "Book a flight to New York on Monday",
        "Tell me a joke",
        "Add a dentist appointment on March 5th"
    ]

    valid_categories = {"Task", "Event", "AI Question"}

    for user_input in test_inputs:
        payload = {"input_text": user_input}
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=TIMEOUT)
        except requests.RequestException as e:
            assert False, f"Request failed for input '{user_input}': {e}"

        assert response.status_code == 200, f"Unexpected status code {response.status_code} for input '{user_input}'"

        try:
            data = response.json()
        except ValueError:
            assert False, f"Response is not valid JSON for input '{user_input}'"

        # Expected response schema:
        # {
        #   "category": "Task" | "Event" | "AI Question",
        #   "confidence": float,
        #   "reasoning": str (optional)
        # }

        category = data.get("category")
        confidence = data.get("confidence")

        assert category in valid_categories, f"Invalid category '{category}' for input '{user_input}'"
        assert isinstance(confidence, (float, int)), f"Confidence is not a number for input '{user_input}'"
        assert confidence > 0.7, f"Confidence {confidence} too low for input '{user_input}'"

test_ai_orchestrator_input_categorization()