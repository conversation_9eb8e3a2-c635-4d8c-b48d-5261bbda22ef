import requests
import uuid

BASE_URL = "http://localhost:3000"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30

def test_task_management_crud_operations():
    task_id = None
    try:
        # 1. Create a new task
        create_payload = {
            "title": "Test Task " + str(uuid.uuid4()),
            "description": "This is a test task for CRUD operations.",
            "due_date": "2025-12-31T23:59:59Z",
            "completed": False
        }
        create_resp = requests.post(f"{BASE_URL}/tasks", json=create_payload, headers=HEADERS, timeout=TIMEOUT)
        assert create_resp.status_code == 201, f"Task creation failed: {create_resp.text}"
        created_task = create_resp.json()
        task_id = created_task.get("id")
        assert task_id is not None, "Created task ID is missing"
        # Validate AI-generated category and priority color coding presence
        assert "category" in created_task, "Task category missing in response"
        assert isinstance(created_task["category"], str) and created_task["category"], "Invalid task category"
        assert "priority_color" in created_task, "Priority color coding missing in response"
        assert isinstance(created_task["priority_color"], str) and created_task["priority_color"], "Invalid priority color"

        # 2. Read the created task
        read_resp = requests.get(f"{BASE_URL}/tasks/{task_id}", headers=HEADERS, timeout=TIMEOUT)
        assert read_resp.status_code == 200, f"Task retrieval failed: {read_resp.text}"
        read_task = read_resp.json()
        assert read_task["id"] == task_id, "Retrieved task ID mismatch"
        assert read_task["title"] == create_payload["title"], "Task title mismatch"
        assert read_task["description"] == create_payload["description"], "Task description mismatch"
        assert read_task["completed"] == create_payload["completed"], "Task completion status mismatch"
        assert read_task["category"] == created_task["category"], "Task category mismatch"
        assert read_task["priority_color"] == created_task["priority_color"], "Priority color mismatch"

        # 3. Update the task (change title, description, completed status)
        update_payload = {
            "title": create_payload["title"] + " - Updated",
            "description": create_payload["description"] + " Updated description.",
            "completed": True
        }
        update_resp = requests.put(f"{BASE_URL}/tasks/{task_id}", json=update_payload, headers=HEADERS, timeout=TIMEOUT)
        assert update_resp.status_code == 200, f"Task update failed: {update_resp.text}"
        updated_task = update_resp.json()
        assert updated_task["title"] == update_payload["title"], "Updated title mismatch"
        assert updated_task["description"] == update_payload["description"], "Updated description mismatch"
        assert updated_task["completed"] == update_payload["completed"], "Updated completion status mismatch"
        # Category and priority color should persist or be updated by AI - check presence and type
        assert "category" in updated_task and isinstance(updated_task["category"], str) and updated_task["category"], "Updated task category invalid"
        assert "priority_color" in updated_task and isinstance(updated_task["priority_color"], str) and updated_task["priority_color"], "Updated priority color invalid"

        # 4. Delete the task
        delete_resp = requests.delete(f"{BASE_URL}/tasks/{task_id}", headers=HEADERS, timeout=TIMEOUT)
        assert delete_resp.status_code == 204, f"Task deletion failed: {delete_resp.text}"

        # 5. Confirm deletion by attempting to read the task again
        confirm_resp = requests.get(f"{BASE_URL}/tasks/{task_id}", headers=HEADERS, timeout=TIMEOUT)
        assert confirm_resp.status_code == 404, "Deleted task still accessible"

    except requests.RequestException as e:
        assert False, f"HTTP request failed: {e}"
    finally:
        # Cleanup: If task still exists, delete it
        if task_id:
            try:
                requests.delete(f"{BASE_URL}/tasks/{task_id}", headers=HEADERS, timeout=TIMEOUT)
            except Exception:
                pass

test_task_management_crud_operations()