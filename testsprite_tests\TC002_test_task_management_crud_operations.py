import requests
import uuid

BASE_URL = "http://localhost:3000"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30

def test_task_management_crud_operations():
    task_id = None
    try:
        # 1. Create a new task
        create_payload = {
            "title": "Test Task " + str(uuid.uuid4()),
            "description": "This is a test task for CRUD operations.",
            "due_date": "2025-12-31T23:59:59Z",
            "priority": "medium",
            "status": "pending"
        }
        create_resp = requests.post(f"{BASE_URL}/api/tasks", json=create_payload, headers=HEADERS, timeout=TIMEOUT)
        assert create_resp.status_code == 200, f"Task creation failed: {create_resp.text}"
        created_response = create_resp.json()
        assert created_response.get("success") == True, "Task creation was not successful"
        task_id = created_response.get("task_id")
        assert task_id is not None, "Created task ID is missing"
        created_task = created_response.get("task", {})
        # Validate task data structure
        assert "title" in created_task, "Task title missing in response"
        assert "description" in created_task, "Task description missing in response"
        assert "priority" in created_task, "Task priority missing in response"

        # 2. Read the created task (get all tasks and find ours)
        read_resp = requests.get(f"{BASE_URL}/api/tasks", headers=HEADERS, timeout=TIMEOUT)
        assert read_resp.status_code == 200, f"Task retrieval failed: {read_resp.text}"
        read_response = read_resp.json()
        assert read_response.get("success") == True, "Task retrieval was not successful"
        tasks = read_response.get("tasks", [])
        read_task = None
        for task in tasks:
            if task.get("id") == task_id:
                read_task = task
                break
        assert read_task is not None, f"Created task with ID {task_id} not found in task list"
        assert read_task["title"] == create_payload["title"], "Task title mismatch"
        assert read_task["description"] == create_payload["description"], "Task description mismatch"
        assert read_task["priority"] == create_payload["priority"], "Task priority mismatch"

        # 3. Update the task (change title, description, status)
        update_payload = {
            "task_id": task_id,
            "title": create_payload["title"] + " - Updated",
            "description": create_payload["description"] + " Updated description.",
            "status": "completed"
        }
        update_resp = requests.put(f"{BASE_URL}/api/tasks/{task_id}", json=update_payload, headers=HEADERS, timeout=TIMEOUT)
        assert update_resp.status_code == 200, f"Task update failed: {update_resp.text}"
        updated_response = update_resp.json()
        assert updated_response.get("success") == True, "Task update was not successful"
        updated_task = updated_response.get("task", {})
        assert updated_task["title"] == update_payload["title"], "Updated title mismatch"
        assert updated_task["description"] == update_payload["description"], "Updated description mismatch"
        assert updated_task["status"] == update_payload["status"], "Updated status mismatch"
        # Priority should persist
        assert "priority" in updated_task, "Updated task priority missing"

        # 4. Delete the task
        delete_resp = requests.delete(f"{BASE_URL}/api/tasks/{task_id}", headers=HEADERS, timeout=TIMEOUT)
        assert delete_resp.status_code == 200, f"Task deletion failed: {delete_resp.text}"
        delete_response = delete_resp.json()
        assert delete_response.get("success") == True, "Task deletion was not successful"

        # 5. Confirm deletion by checking task list
        confirm_resp = requests.get(f"{BASE_URL}/api/tasks", headers=HEADERS, timeout=TIMEOUT)
        assert confirm_resp.status_code == 200, "Failed to get tasks after deletion"
        confirm_response = confirm_resp.json()
        remaining_tasks = confirm_response.get("tasks", [])
        deleted_task_found = any(task.get("id") == task_id for task in remaining_tasks)
        assert not deleted_task_found, "Deleted task still found in task list"

    except requests.RequestException as e:
        assert False, f"HTTP request failed: {e}"
    finally:
        # Cleanup: If task still exists, delete it
        if task_id:
            try:
                requests.delete(f"{BASE_URL}/api/tasks/{task_id}", headers=HEADERS, timeout=TIMEOUT)
            except Exception:
                pass

test_task_management_crud_operations()