import requests
import uuid

BASE_URL = "http://localhost:3000"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30


def test_task_management_crud_operations():
    task_id = None
    try:
        # 1. Create a new task
        create_payload = {
            "title": "Test Task " + str(uuid.uuid4()),
            "description": "This is a test task created for CRUD operations validation.",
            "priority": "medium"  # Assuming priority can be low, medium, high
        }
        # due_date is optional and should be omitted if None
        due_date = None
        if due_date is not None:
            create_payload["due_date"] = due_date

        create_resp = requests.post(
            f"{BASE_URL}/tasks",
            json=create_payload,
            headers=HEADERS,
            timeout=TIMEOUT,
        )
        assert create_resp.status_code == 201, f"Task creation failed: {create_resp.text}"
        created_task = create_resp.json()
        task_id = created_task.get("id")
        assert task_id is not None, "Created task ID is missing"
        # Validate AI-generated category and priority color coding presence
        assert "category" in created_task, "AI-generated category missing in created task"
        assert created_task["category"] == "Task", "Created task category is not 'Task'"
        assert "priority_color" in created_task, "Priority color coding missing in created task"
        assert created_task["priority_color"] in ["red", "yellow", "green"], "Invalid priority color coding"

        # 2. Read the created task
        read_resp = requests.get(
            f"{BASE_URL}/tasks/{task_id}",
            headers=HEADERS,
            timeout=TIMEOUT,
        )
        assert read_resp.status_code == 200, f"Task read failed: {read_resp.text}"
        read_task = read_resp.json()
        assert read_task["id"] == task_id, "Read task ID mismatch"
        assert read_task["title"] == create_payload["title"], "Read task title mismatch"
        assert read_task["category"] == "Task", "Read task category mismatch"
        assert read_task["priority_color"] == created_task["priority_color"], "Read task priority color mismatch"

        # 3. Update the task - change title, priority and status
        update_payload = {
            "title": create_payload["title"] + " - Updated",
            "priority": "high",
            "status": "in_progress",
        }
        update_resp = requests.put(
            f"{BASE_URL}/tasks/{task_id}",
            json=update_payload,
            headers=HEADERS,
            timeout=TIMEOUT,
        )
        assert update_resp.status_code == 200, f"Task update failed: {update_resp.text}"
        updated_task = update_resp.json()
        assert updated_task["title"] == update_payload["title"], "Updated task title mismatch"
        assert updated_task["status"] == update_payload["status"], "Updated task status mismatch"
        # Priority color coding should reflect updated priority
        assert "priority_color" in updated_task, "Priority color missing after update"
        # Assuming priority_color changes accordingly, e.g. high -> red
        priority_color_map = {"low": "green", "medium": "yellow", "high": "red"}
        expected_color = priority_color_map.get(update_payload["priority"])
        assert updated_task["priority_color"] == expected_color, "Priority color coding incorrect after update"

        # 4. List tasks and verify the updated task is present with correct data
        list_resp = requests.get(
            f"{BASE_URL}/tasks",
            headers=HEADERS,
            timeout=TIMEOUT,
        )
        assert list_resp.status_code == 200, f"Task list retrieval failed: {list_resp.text}"
        tasks_list = list_resp.json()
        found = False
        for task in tasks_list:
            if task.get("id") == task_id:
                found = True
                assert task["title"] == update_payload["title"], "Listed task title mismatch"
                assert task["status"] == update_payload["status"], "Listed task status mismatch"
                assert task["priority_color"] == expected_color, "Listed task priority color mismatch"
                assert task["category"] == "Task", "Listed task category mismatch"
                break
        assert found, "Updated task not found in task list"

    finally:
        # 5. Delete the created task
        if task_id:
            del_resp = requests.delete(
                f"{BASE_URL}/tasks/{task_id}",
                headers=HEADERS,
                timeout=TIMEOUT,
            )
            assert del_resp.status_code in [200, 204], f"Task deletion failed: {del_resp.text}"
            # Verify deletion by attempting to get the task again
            get_after_del_resp = requests.get(
                f"{BASE_URL}/tasks/{task_id}",
                headers=HEADERS,
                timeout=TIMEOUT,
            )
            assert get_after_del_resp.status_code == 404, "Deleted task still accessible"


test_task_management_crud_operations()
