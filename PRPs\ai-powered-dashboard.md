name: "AI-Powered Dashboard with Visual Transparency & Smooth Animations"
description: |
  Build a modern, personal AI-powered dashboard with incredibly smooth animations and visual feedback.
  Features intelligent input categorization (Tasks, Events, AI Questions), semantic search, web search,
  and calendar/task management - all orchestrated through Mirascope agents with complete visual transparency.

## Purpose
Create a production-ready dashboard that feels "alive" through smooth animations while providing
intelligent AI-powered organization of user inputs through advanced visual feedback systems.

## Core Principles
1. **Context is King**: Include ALL necessary documentation, examples, and caveats
2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
3. **Information Dense**: Use keywords and patterns from the codebase
4. **Progressive Success**: Start simple, validate, then enhance
5. **Global rules**: Follow all rules in CLAUDE.md and the mermaid diagram EXACTLY

---

## Goal
Build a modern personal dashboard (no auth) with black/grey/white design, featuring:
- **Hero Input Bar**: AI categorizes any input into Tasks/Events/Questions
- **Visual Transparency**: User sees every AI step with smooth animations  
- **Smart Components**: Calendar integration, task management, semantic search
- **Smooth 60fps animations**: Using Framer Motion throughout
- **Persistence**: State survives refresh, no sessions

## Why
- **Business value**: Streamlines personal productivity through intelligent AI categorization
- **User impact**: Eliminates manual organization - AI handles everything intelligently
- **Integration**: Combines multiple AI tools (search, calendar, tasks) in unified interface
- **Innovation**: Pure AI decision-making without hardcoded keywords

## What
A React-based dashboard where users input anything and AI intelligently:
1. **Categorizes** input (Task/Event/Question) with visual feedback
2. **Processes** through appropriate tools (calendar/tasks/search/web)
3. **Displays** results with smooth animations and transparency
4. **Persists** state across sessions with SQLite + embeddings

### Success Criteria
- [ ] Input bar accepts any text and categorizes intelligently (no keywords)
- [ ] Visual animations show every AI processing step (per mermaid diagram)
- [ ] Tasks auto-categorized and added to smart lists
- [ ] Events/reminders sync with integrated calendar
- [ ] AI questions trigger appropriate search tools (semantic/web/knowledge)
- [ ] All animations are smooth 60fps with spring physics
- [ ] State persists across page refreshes
- [ ] Docker container runs perfectly for testing
- [ ] OpenRouter free models work with adapted tool calling

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- docfile: research/mirascope/llms-full.txt
  why: Complete Mirascope API reference and patterns
  
- docfile: research/mirascope/page7-agents.md
  why: Agent creation and state management patterns
  
- docfile: research/mirascope/page6-tools.md
  why: Tool registration and calling patterns for calendar/tasks/search
  
- docfile: research/mirascope/page5-streams.md
  why: Streaming for real-time visual feedback to user
  
- docfile: research/mirascope/page9-response-models.md
  why: Structured responses for non-tool-calling models (OpenRouter)
  
- docfile: research/mirascope/page11-json-mode.md
  why: JSON parsing for free models that don't support native tool calling
  
- docfile: research/mirascope/page10-local-models.md
  why: OpenRouter/OpenAI compatible setup patterns
  
- docfile: research/react/page1-learn-react.md
  why: Modern React patterns for dashboard components
  
- docfile: research/react/page2-state-management.md
  why: State management for persistent UI state
  
- docfile: research/framer-motion/page1-main.md
  why: Animation library setup and basic usage
  
- docfile: research/framer-motion/page3-animate.md
  why: Animation functions for smooth 60fps effects
  
- docfile: research/framer-motion/page4-transitions.md
  why: Transition patterns for visual feedback
  
- docfile: research/tailwind/page1-main-docs.md
  why: Utility-first CSS for black/grey/white styling
  
- docfile: research/react-hook-form/page2-get-started.md
  why: Efficient input handling for hero bar
  
- docfile: research/langsearch/page1-web-search-api.md
  why: Web search integration for AI questions
  
- file: examples/ai_orchestrator_flow.mermaid
  why: CRITICAL - Must follow this animation flow diagram EXACTLY
  critical: Every animation step must match this workflow
```

### Current Codebase tree
```bash
.
├── examples/
│   └── ai_orchestrator_flow.mermaid  # CRITICAL workflow diagram
├── PRPs/
│   ├── templates/
│   │   └── prp_base.md
│   └── EXAMPLE_multi_agent_prp.md
├── research/                         # Fresh documentation scraped
│   ├── mirascope/                   # Complete Mirascope docs
│   ├── react/                       # React patterns  
│   ├── framer-motion/               # Animation guides
│   ├── tailwind/                    # Styling docs
│   ├── react-hook-form/             # Form handling
│   ├── langsearch/                  # Web search API
│   └── ollama/                      # Embeddings models
├── INITIAL.md                       # Project requirements
├── CLAUDE.md                        # Development guidelines
└── README.md
```

### Desired Codebase tree with files to be added
```bash
.
├── frontend/                        # React application
│   ├── public/
│   │   └── index.html
│   ├── src/
│   │   ├── components/
│   │   │   ├── layout/
│   │   │   │   ├── Sidebar.tsx      # Navigation sidebar
│   │   │   │   └── Layout.tsx       # Main layout wrapper
│   │   │   ├── dashboard/
│   │   │   │   ├── HeroInputBar.tsx # Main input with animations
│   │   │   │   ├── Dashboard.tsx    # Main dashboard component
│   │   │   │   └── VisualFeedback.tsx # AI processing animations
│   │   │   ├── tasks/
│   │   │   │   ├── TaskList.tsx     # Smart categorized task list
│   │   │   │   ├── TaskItem.tsx     # Individual task component
│   │   │   │   └── TaskFilters.tsx  # Filter functionality
│   │   │   ├── calendar/
│   │   │   │   ├── Calendar.tsx     # Integrated calendar
│   │   │   │   ├── EventView.tsx    # Event display
│   │   │   │   └── DatePicker.tsx   # Date selection
│   │   │   ├── ai-response/
│   │   │   │   ├── AIResponsePanel.tsx # Answer display
│   │   │   │   ├── SearchResults.tsx   # Search results
│   │   │   │   └── SourceLinks.tsx     # Web search sources
│   │   │   └── settings/
│   │   │       ├── Settings.tsx     # Settings page
│   │   │       ├── APISettings.tsx  # LLM configuration
│   │   │       └── EmbeddingsStatus.tsx # Index status
│   │   ├── hooks/
│   │   │   ├── useAIOrchestrator.ts # Main AI workflow hook
│   │   │   ├── useAnimations.ts     # Animation state management
│   │   │   ├── usePersistence.ts    # Local storage/SQLite
│   │   │   └── useWebSocket.ts      # Real-time updates
│   │   ├── services/
│   │   │   ├── aiService.ts         # Backend AI communication
│   │   │   ├── storageService.ts    # SQLite operations
│   │   │   └── embeddingsService.ts # Ollama embeddings
│   │   ├── types/
│   │   │   ├── index.ts             # All TypeScript types
│   │   │   ├── ai.ts                # AI response types
│   │   │   └── animations.ts        # Animation state types
│   │   ├── utils/
│   │   │   ├── animations.ts        # Animation utilities
│   │   │   └── constants.ts         # App constants
│   │   ├── styles/
│   │   │   └── globals.css          # Global Tailwind styles
│   │   ├── App.tsx                  # Root component
│   │   └── main.tsx                 # React entry point
│   ├── package.json
│   ├── tailwind.config.js           # Black/grey/white theme
│   ├── tsconfig.json
│   └── vite.config.ts               # Vite configuration
├── backend/                         # Python AI orchestration
│   ├── app/
│   │   ├── agents/
│   │   │   ├── __init__.py
│   │   │   ├── orchestrator.py      # Main categorization agent
│   │   │   ├── task_agent.py        # Task processing agent
│   │   │   ├── calendar_agent.py    # Calendar/event agent
│   │   │   └── search_agent.py      # Question answering agent
│   │   ├── tools/
│   │   │   ├── __init__.py
│   │   │   ├── calendar_tool.py     # Calendar operations
│   │   │   ├── task_tool.py         # Task management
│   │   │   ├── web_search_tool.py   # LangSearch integration
│   │   │   ├── database_search_tool.py # Semantic search
│   │   │   └── embedding_tool.py    # Ollama embeddings
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── pydantic_models.py   # Data validation models
│   │   │   ├── database.py          # SQLite models
│   │   │   └── ai_responses.py      # AI response schemas
│   │   ├── api/
│   │   │   ├── __init__.py
│   │   │   ├── routes.py            # FastAPI routes
│   │   │   ├── websockets.py        # Real-time updates
│   │   │   └── middleware.py        # CORS, auth, etc.
│   │   ├── config/
│   │   │   ├── __init__.py
│   │   │   └── settings.py          # Environment configuration
│   │   ├── database/
│   │   │   ├── __init__.py
│   │   │   ├── connection.py        # SQLite connection
│   │   │   ├── migrations.py        # Database migrations
│   │   │   └── seed_data.py         # Initial data
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── llm_service.py       # Mirascope LLM calls
│   │   │   ├── embedding_service.py # Ollama embeddings
│   │   │   └── persistence_service.py # Data persistence
│   │   └── main.py                  # FastAPI application
│   ├── requirements.txt             # Python dependencies
│   ├── pyproject.toml               # Python project config
│   └── Dockerfile                   # Backend container
├── docker/
│   ├── docker-compose.yml           # Full stack orchestration
│   ├── frontend.Dockerfile          # Frontend container
│   └── ollama.Dockerfile            # Ollama embeddings container
├── tests/
│   ├── frontend/                    # Jest/React Testing Library
│   │   ├── components/
│   │   ├── hooks/
│   │   └── services/
│   ├── backend/                     # Pytest tests
│   │   ├── test_agents/
│   │   ├── test_tools/
│   │   ├── test_api/
│   │   └── test_models/
│   └── e2e/                         # Playwright end-to-end tests
│       ├── test_workflow.py
│       └── test_animations.py
├── .env.example                     # Environment template
├── README.md                        # Comprehensive documentation
└── package.json                     # Root package.json for scripts
```

### Known Gotchas & Library Quirks
```python
# CRITICAL: OpenRouter free models DON'T support native tool calling
# Solution: Use Mirascope response_models + JSON mode for structured output

# CRITICAL: Framer Motion requires specific React 18+ patterns for smooth performance
# Pattern: Use transform3d and will-change CSS properties for GPU acceleration

# CRITICAL: Mirascope streams require async iteration and proper error handling
# Pattern: Always wrap stream processing in try/catch blocks

# CRITICAL: SQLite needs WAL mode for concurrent read/writes in web context
# Pattern: Enable WAL mode during database initialization

# CRITICAL: Ollama embeddings need model pre-download for performance
# Pattern: Include model pull in Docker setup scripts

# CRITICAL: React Hook Form needs proper TypeScript typing for validation
# Pattern: Define interfaces for all form schemas

# CRITICAL: Tailwind animations need duration/timing-function optimization
# Pattern: Use custom animation curves for organic feel

# CRITICAL: WebSocket connections need reconnection logic for reliability
# Pattern: Implement exponential backoff for reconnections

# CRITICAL: State persistence needs cleanup to prevent memory leaks
# Pattern: Use cleanup functions in React hooks and database connections
```

## Implementation Blueprint

### Data models and structure

```python
# models/pydantic_models.py - Core data structures
from pydantic import BaseModel, Field
from typing import List, Optional, Union, Literal
from datetime import datetime
from enum import Enum

class InputCategory(str, Enum):
    """AI-determined input categories"""
    TASK = "task"
    EVENT = "event" 
    AI_QUESTION = "ai_question"

class TaskPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class QuestionType(str, Enum):
    """AI-determined question sub-categories"""
    SIMPLE_KNOWLEDGE = "simple_knowledge"
    DATABASE_SEARCH = "database_search"
    WEB_SEARCH = "web_search"

class UserInput(BaseModel):
    """Raw user input from hero bar"""
    text: str = Field(..., min_length=1, description="User's input text")
    timestamp: datetime = Field(default_factory=datetime.now)

class CategoryDecision(BaseModel):
    """AI categorization result"""
    category: InputCategory = Field(..., description="Determined category")
    confidence: float = Field(..., ge=0.0, le=1.0, description="AI confidence score")
    reasoning: str = Field(..., description="Why this category was chosen")
    processing_steps: List[str] = Field(default=[], description="Visual feedback steps")

class Task(BaseModel):
    """Task model"""
    id: Optional[str] = None
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    category: Optional[str] = None  # AI-generated category
    priority: TaskPriority = TaskPriority.MEDIUM
    due_date: Optional[datetime] = None
    completed: bool = False
    created_at: datetime = Field(default_factory=datetime.now)
    ai_generated_category: Optional[str] = None

class Event(BaseModel):
    """Calendar event model"""
    id: Optional[str] = None
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    start_time: datetime
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)

class AIQuestion(BaseModel):
    """AI question processing"""
    text: str = Field(..., min_length=1)
    question_type: QuestionType
    reasoning: str = Field(..., description="Why this question type")
    
class SearchResult(BaseModel):
    """Search result from web or database"""
    title: str
    content: str
    source: str
    url: Optional[str] = None
    relevance_score: float = Field(..., ge=0.0, le=1.0)

class AIResponse(BaseModel):
    """Final AI response to user"""
    original_input: str
    category: InputCategory
    processing_steps: List[str]  # For visual feedback
    result: Union[Task, Event, str]  # Task/Event object or answer string
    search_results: Optional[List[SearchResult]] = None
    sources: Optional[List[str]] = None
    confidence: float = Field(..., ge=0.0, le=1.0)

# Animation state models
class AnimationStep(BaseModel):
    """Individual animation step"""
    id: str
    message: str
    animation_type: str  # "pulse", "spin", "typewriter", etc.
    duration: float = 1.0
    completed: bool = False

class ProcessingState(BaseModel):
    """Overall processing state for animations"""
    current_step: int = 0
    steps: List[AnimationStep] = []
    is_processing: bool = False
    error: Optional[str] = None
```

### List of tasks to be completed

```yaml
Task 1: Project Setup & Environment Configuration
CREATE docker-compose.yml:
  - PATTERN: Multi-service setup with frontend, backend, ollama
  - Include environment variables for all services
  - Set up volumes for persistent data
  - Configure networking between services

CREATE .env.example:
  - Include all API keys (OpenRouter, LangSearch)
  - Database configurations
  - Model settings for Ollama
  - Development vs production flags

Task 2: Backend Core Setup - FastAPI + Mirascope
CREATE backend/app/main.py:
  - PATTERN: Standard FastAPI setup with CORS, middleware
  - WebSocket support for real-time updates
  - Health check endpoints
  - Error handling middleware

CREATE backend/app/config/settings.py:
  - PATTERN: Pydantic settings with environment validation
  - LLM provider configurations (OpenRouter)
  - Database connection settings
  - API rate limiting configurations

Task 3: Database Layer - SQLite with Persistence
CREATE backend/app/database/connection.py:
  - PATTERN: SQLAlchemy async engine setup
  - Enable WAL mode for concurrent access
  - Connection pooling for performance
  - Proper cleanup and session management

CREATE backend/app/database/migrations.py:
  - PATTERN: Alembic-style migrations for tables
  - Tasks table with AI-generated categories
  - Events table with datetime handling
  - User inputs table for persistence
  - Embeddings table for semantic search

Task 4: Mirascope Agent Architecture - Core Orchestrator
CREATE backend/app/agents/orchestrator.py:
  - PATTERN: Main categorization agent following research/mirascope/page7-agents.md
  - Use response_model for structured categorization (non-tool calling models)
  - Implement streaming for real-time feedback per mermaid diagram
  - State management for conversation context
  - Error handling and retry logic

Task 5: Specialized Agents - Task Processing
CREATE backend/app/agents/task_agent.py:
  - PATTERN: Agent specialized for task extraction and categorization  
  - Use Mirascope tools for task operations
  - Auto-categorization logic (AI-driven, no hardcoding)
  - Date extraction and parsing for due dates
  - Integration with task_tool for database operations

Task 6: Specialized Agents - Calendar/Event Processing  
CREATE backend/app/agents/calendar_agent.py:
  - PATTERN: Agent for event/reminder processing
  - Date/time extraction and validation
  - Handle incomplete datetime information (ask user)
  - Timezone handling and conflicts detection
  - Integration with calendar_tool for operations

Task 7: Specialized Agents - AI Question Processing
CREATE backend/app/agents/search_agent.py:
  - PATTERN: Agent that decides question type and routes appropriately
  - Use multiple tools: database_search, web_search, simple knowledge
  - No keyword hardcoding - pure AI decision making
  - Confidence scoring for tool selection
  - Result synthesis and source attribution

Task 8: Tool Implementation - Calendar Operations
CREATE backend/app/tools/calendar_tool.py:
  - PATTERN: Mirascope tool following research/mirascope/page6-tools.md
  - CRUD operations for events (create, read, update, delete)
  - Conflict detection and resolution
  - Integration with database layer
  - Return structured data for agent consumption

Task 9: Tool Implementation - Task Management
CREATE backend/app/tools/task_tool.py:
  - PATTERN: Mirascope tool for task operations
  - Smart categorization suggestions
  - Priority assignment based on content
  - Due date parsing and suggestions
  - Bulk operations for efficiency

Task 10: Tool Implementation - Semantic Database Search
CREATE backend/app/tools/database_search_tool.py:
  - PATTERN: Vector similarity search using Ollama embeddings
  - Query embedding generation
  - Similarity scoring and ranking
  - Result formatting for AI consumption
  - Index management and updates

Task 11: Tool Implementation - Web Search Integration
CREATE backend/app/tools/web_search_tool.py:
  - PATTERN: LangSearch API integration following research/langsearch/page1-web-search-api.md
  - Multi-site search with source attribution
  - Result ranking and filtering
  - Rate limiting and error handling
  - Structured response formatting

Task 12: Tool Implementation - Ollama Embeddings
CREATE backend/app/tools/embedding_tool.py:
  - PATTERN: Ollama model integration for embeddings
  - Model management (download, load, unload)
  - Batch embedding processing
  - Vector storage and retrieval
  - Performance optimization

Task 13: API Routes - Core Endpoints
CREATE backend/app/api/routes.py:
  - PATTERN: FastAPI router with proper HTTP methods
  - POST /api/process-input - main orchestration endpoint
  - GET/POST /api/tasks - task management endpoints  
  - GET/POST /api/events - calendar endpoints
  - GET /api/settings - configuration endpoints
  - Proper error handling and validation

Task 14: WebSocket Implementation - Real-time Updates
CREATE backend/app/api/websockets.py:
  - PATTERN: WebSocket connection management  
  - Real-time processing updates (per mermaid diagram)
  - Connection lifecycle management
  - Error handling and reconnection logic
  - Message broadcasting to multiple clients

Task 15: Frontend Core Setup - React + Vite
CREATE frontend/package.json:
  - PATTERN: Modern React setup with TypeScript
  - Dependencies: React 18+, Framer Motion, Tailwind, React Hook Form
  - Development tools: Vite, ESLint, Prettier
  - Testing: Jest, React Testing Library

CREATE frontend/src/App.tsx:
  - PATTERN: Root component with routing
  - Layout wrapper with sidebar
  - Global state management setup
  - Error boundaries for robustness

Task 16: Layout Components - Sidebar and Navigation
CREATE frontend/src/components/layout/Sidebar.tsx:
  - PATTERN: Fixed sidebar with navigation following research/react patterns
  - Framer Motion animations for menu items
  - Active state management
  - Responsive design with collapse functionality

CREATE frontend/src/components/layout/Layout.tsx:
  - PATTERN: Main layout wrapper
  - Sidebar integration with main content area
  - Responsive layout handling
  - Global loading states

Task 17: Hero Input Bar - Main Feature Implementation  
CREATE frontend/src/components/dashboard/HeroInputBar.tsx:
  - PATTERN: React Hook Form integration following research/react-hook-form/page2-get-started.md
  - Real-time input handling with persistence
  - Submission with loading states
  - Integration with AI service
  - Form validation and error handling

Task 18: Visual Feedback System - Animation Orchestration
CREATE frontend/src/components/dashboard/VisualFeedback.tsx:
  - PATTERN: Framer Motion animations following research/framer-motion patterns
  - Implementation of EXACT mermaid diagram workflow
  - Step-by-step visual indicators (analyzing, categorizing, processing)
  - Smooth transitions between states
  - Progress indicators and completion states

Task 19: Animation Hook - State Management
CREATE frontend/src/hooks/useAnimations.ts:
  - PATTERN: Custom hook for animation state management
  - Sequential animation triggering per mermaid diagram
  - Spring physics configuration for organic feel
  - Error state animations (shake, fade, etc.)
  - Success state celebrations (confetti, pulse, etc.)

Task 20: AI Orchestrator Hook - Main Workflow
CREATE frontend/src/hooks/useAIOrchestrator.ts:
  - PATTERN: Main workflow hook integrating all AI operations
  - WebSocket connection management
  - State management for processing steps
  - Error handling and retry logic
  - Result state management
  - INCREDIBLY IMPORTANT: Follow the flow described in the mermaid diagram (examples/ai_orchestrator_flow.mermaid)

Task 21: Task Management Components - Smart Lists
CREATE frontend/src/components/tasks/TaskList.tsx:
  - PATTERN: Dynamic categorization display
  - Framer Motion list animations (grow, slide, stagger)
  - Filter and search functionality
  - Drag and drop reordering
  - Real-time updates from WebSocket

CREATE frontend/src/components/tasks/TaskItem.tsx:
  - PATTERN: Individual task with edit capabilities
  - Completion animations
  - Priority indicators with color coding
  - Due date handling and overdue states
  - Category badges (AI-generated)

Task 22: Calendar Integration - Event Management
CREATE frontend/src/components/calendar/Calendar.tsx:
  - PATTERN: Calendar widget with event display
  - Month/week/day view switching
  - Event creation from calendar interface
  - Drag and drop event management
  - Integration with AI-created events

Task 23: AI Response Panel - Question Answers
CREATE frontend/src/components/ai-response/AIResponsePanel.tsx:
  - PATTERN: Dynamic response display based on question type
  - Typewriter effect for text responses
  - Search results with source attribution
  - Expandable/collapsible sections
  - Copy to clipboard functionality

Task 24: Settings Management - API Configuration
CREATE frontend/src/components/settings/Settings.tsx:
  - PATTERN: Settings page with form validation
  - API key management (masked inputs)
  - Model selection for different providers
  - Embedding status indicators
  - Index management controls

Task 25: Services Layer - API Communication
CREATE frontend/src/services/aiService.ts:
  - PATTERN: Axios-based API client with interceptors
  - WebSocket service integration
  - Request/response transformation
  - Error handling and retry logic
  - Token management for authenticated requests

Task 26: Persistence Hook - Local Storage + SQLite
CREATE frontend/src/hooks/usePersistence.ts:
  - PATTERN: Dual persistence strategy
  - Local storage for UI state (input text, settings)
  - SQLite integration for permanent data
  - Automatic sync between local and remote state
  - Cleanup and memory management

Task 27: Styling System - Tailwind Configuration
CREATE frontend/tailwind.config.js:
  - PATTERN: Custom theme with black/grey/white palette
  - Animation utilities for Framer Motion integration
  - Custom spacing and typography scales
  - Dark theme optimization
  - Component-specific utility classes

Task 28: TypeScript Definitions - Complete Type Safety
CREATE frontend/src/types/index.ts:
  - PATTERN: Comprehensive type definitions
  - API response interfaces
  - Animation state types
  - Form validation schemas
  - WebSocket message types

Task 29: Testing Suite - Frontend Components
CREATE tests/frontend/:
  - PATTERN: Jest + React Testing Library
  - Component unit tests with animation testing
  - Hook testing for complex state management
  - Integration tests for AI workflow
  - Accessibility testing

Task 30: Testing Suite - Backend API
CREATE tests/backend/:
  - PATTERN: Pytest with async support
  - Agent testing with mocked LLM calls
  - Tool testing with database mocking
  - API endpoint testing
  - WebSocket connection testing

Task 31: E2E Testing - Complete Workflow
CREATE tests/e2e/test_workflow.py:
  - PATTERN: Playwright browser automation
  - Complete user journey testing
  - Animation verification
  - Cross-browser testing
  - Performance testing

Task 32: Docker Configuration - Full Stack
CREATE docker/docker-compose.yml:
  - PATTERN: Multi-service orchestration
  - Frontend (Vite dev server)
  - Backend (FastAPI with hot reload)  
  - Ollama (embeddings service)
  - Database (SQLite with volume mounting)

Task 33: Performance Optimization - Frontend
OPTIMIZE frontend performance:
  - PATTERN: Code splitting and lazy loading
  - Framer Motion GPU acceleration optimizations
  - React.memo and useMemo for expensive operations
  - Bundle size optimization
  - Service worker for caching

Task 34: Performance Optimization - Backend
OPTIMIZE backend performance:
  - PATTERN: Async/await throughout Mirascope calls
  - Connection pooling for database
  - Caching for embeddings and frequent queries
  - Background task processing
  - Rate limiting implementation

Task 35: Documentation - Comprehensive README
CREATE README.md:
  - PATTERN: Complete setup and usage guide
  - Architecture diagram with mermaid
  - API documentation
  - Development workflow
  - Deployment instructions

Task 36: Environment Setup - Development
CREATE development environment:
  - PATTERN: Docker development setup
  - Hot reloading for both frontend and backend
  - Database migration scripts
  - Seed data for testing
  - Health checks and monitoring

Task 37: Error Handling - Robust Error Management
IMPLEMENT comprehensive error handling:
  - PATTERN: Graceful degradation throughout
  - User-friendly error messages
  - Retry mechanisms with exponential backoff
  - Logging and monitoring integration
  - Recovery strategies for failed operations

Task 38: Security Implementation - API Security
IMPLEMENT security measures:
  - PATTERN: API rate limiting and validation
  - Input sanitization and validation
  - CORS configuration for production
  - Environment variable protection
  - SQL injection prevention

Task 39: Production Deployment - Docker Production
CREATE production deployment:
  - PATTERN: Multi-stage Docker builds
  - Environment-specific configurations
  - Health checks and restart policies
  - Volume management for data persistence
  - Nginx reverse proxy configuration

Task 40: Final Integration Testing - Complete System
PERFORM final integration testing:
  - PATTERN: End-to-end system validation
  - Performance benchmarking
  - Animation smoothness verification
  - Data persistence validation
  - Cross-browser compatibility testing
```

### Per task pseudocode

```python
# Task 4: Mirascope Agent Architecture - Core Orchestrator
# research/mirascope/page7-agents.md pattern with OpenRouter adaptation

from mirascope import llm
from app.models.pydantic_models import CategoryDecision, UserInput, InputCategory
import asyncio

class OrchestratorAgent:
    """Main categorization agent with visual feedback streaming"""
    
    def __init__(self):
        self.history = []  # Conversation memory
        self.processing_steps = []  # For visual feedback
        
    @llm.call(
        provider="openai",  # OpenRouter is OpenAI compatible
        model="qwen/qwen3-235b-a22b-07-25:free",  # Free model
        response_model=CategoryDecision,  # Structured output for non-tool calling
        stream=True  # CRITICAL: For real-time visual feedback
    )
    async def categorize_input(self, user_input: UserInput) -> str:
        """
        Analyze user input and categorize into Task, Event, or AI Question.
        CRITICAL: No keyword hardcoding - pure AI decision making.
        """
        return f"""
        Analyze this user input and categorize it into one of three categories:
        1. TASK - Something the user needs to do (homework, chores, work items)
        2. EVENT - Scheduled items, reminders, appointments with dates/times  
        3. AI_QUESTION - Questions that need answers (facts, explanations, research)
        
        User input: "{user_input.text}"
        
        Think step by step:
        1. What is the user trying to accomplish?
        2. Does this involve scheduling/time? -> EVENT
        3. Does this involve doing something? -> TASK  
        4. Does this involve getting information? -> AI_QUESTION
        
        Provide your categorization with reasoning and confidence score.
        """
    
    async def process_with_visual_feedback(self, user_input: UserInput):
        """
        PATTERN: Follow examples/ai_orchestrator_flow.mermaid EXACTLY
        Stream processing updates for visual transparency
        """
        # Step 1: Analyzing input animation
        yield {"step": "analyzing", "message": "Analyzing input...", "animation": "typing_indicator"}
        
        # Step 2: Categorizing animation  
        yield {"step": "categorizing", "message": "Categorizing...", "animation": "brain_animation"}
        
        # Step 3: Get AI categorization
        async for chunk, _ in self.categorize_input(user_input):
            if chunk.content:
                category_data = json.loads(chunk.content)
                
        # Step 4: Route to appropriate agent with visual feedback
        if category_data.category == InputCategory.TASK:
            yield {"step": "task_identified", "message": "Task identified!", "animation": "green_pulse"}
            result = await self._process_task(user_input, category_data)
        elif category_data.category == InputCategory.EVENT:
            yield {"step": "event_identified", "message": "Event identified!", "animation": "blue_pulse"}  
            result = await self._process_event(user_input, category_data)
        else:
            yield {"step": "question_identified", "message": "Question identified!", "animation": "purple_pulse"}
            result = await self._process_question(user_input, category_data)
            
        # Final step: Success animation
        yield {"step": "complete", "message": "Processing complete!", "animation": "success_confetti"}
        return result

# Task 17: Hero Input Bar Implementation
# research/react-hook-form/page2-get-started.md pattern with persistence

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion, AnimatePresence } from 'framer-motion';
import { useAIOrchestrator } from '../hooks/useAIOrchestrator';
import { usePersistence } from '../hooks/usePersistence';

interface HeroInputProps {
  onSubmit: (input: string) => void;
}

export function HeroInputBar({ onSubmit }: HeroInputProps) {
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: { input: '' }
  });
  
  const { processInput, isProcessing, currentStep } = useAIOrchestrator();
  const { saveInputState, loadInputState } = usePersistence();
  
  // CRITICAL: Persist input state across page refreshes
  const inputValue = watch('input');
  useEffect(() => {
    saveInputState(inputValue);
  }, [inputValue, saveInputState]);
  
  useEffect(() => {
    const savedInput = loadInputState();
    if (savedInput) setValue('input', savedInput);
  }, [setValue, loadInputState]);
  
  const onFormSubmit = async (data: { input: string }) => {
    if (!data.input.trim()) return;
    
    // PATTERN: Clear input and start processing with animations
    setValue('input', '');
    await processInput(data.input);
  };
  
  return (
    <motion.div
      className="w-full max-w-4xl mx-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <form onSubmit={handleSubmit(onFormSubmit)} className="relative">
        {/* CRITICAL: Follow mermaid diagram for animations */}
        <motion.input
          {...register('input', { required: true, minLength: 1 })}
          placeholder="Enter anything - I'll intelligently organize it for you..."
          className="w-full px-6 py-4 text-lg bg-gray-900 border border-gray-700 rounded-lg 
                     focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
                     text-white placeholder-gray-400 transition-all duration-200"
          disabled={isProcessing}
          whileFocus={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 400, damping: 25 }}
        />
        
        {/* Visual feedback overlay */}
        <AnimatePresence>
          {isProcessing && (
            <ProcessingOverlay currentStep={currentStep} />
          )}
        </AnimatePresence>
        
        {/* Submit button with loading state */}
        <motion.button
          type="submit"
          disabled={isProcessing || !inputValue?.trim()}
          className="absolute right-2 top-2 bottom-2 px-4 bg-blue-600 hover:bg-blue-700 
                     disabled:bg-gray-600 text-white rounded-md transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isProcessing ? <Spinner /> : 'Process'}
        </motion.button>
      </form>
    </motion.div>
  );
}

# Task 18: Visual Feedback System 
# research/framer-motion patterns with mermaid diagram implementation

const ProcessingOverlay = ({ currentStep }: { currentStep: string }) => {
  const stepAnimations = {
    analyzing: {
      icon: "🎭",
      message: "Analyzing input...",
      animation: "typing"
    },
    categorizing: {
      icon: "🤖", 
      message: "Categorizing...",
      animation: "brain"
    },
    task_identified: {
      icon: "📝",
      message: "Task identified!",
      animation: "pulse",
      color: "green"
    },
    // ... follow complete mermaid diagram
  };
  
  return (
    <motion.div
      className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-gray-800 px-6 py-4 rounded-lg flex items-center space-x-3"
        initial={{ scale: 0.8, y: 10 }}
        animate={{ scale: 1, y: 0 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        {/* CRITICAL: Implement exact animations from mermaid diagram */}
        <AnimatedIcon 
          icon={stepAnimations[currentStep]?.icon} 
          animation={stepAnimations[currentStep]?.animation}
        />
        <motion.span
          className="text-white"
          key={currentStep}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          {stepAnimations[currentStep]?.message}
        </motion.span>
      </motion.div>
    </motion.div>
  );
};
```

### Integration Points
```yaml
DATABASE:
  - migrations: "Create tasks, events, inputs, embeddings tables"
  - indexes: "CREATE INDEX idx_task_category ON tasks(ai_generated_category)"
  - wal_mode: "PRAGMA journal_mode=WAL for concurrent access"
  
CONFIG:
  - environment: "OpenRouter API key, LangSearch key, Ollama endpoint"
  - models: "qwen/qwen3-235b-a22b-07-25:free for categorization"
  - embeddings: "nomic-embed-text for semantic search via Ollama"
  
API:
  - routes: "/api/process-input, /api/tasks, /api/events, /api/settings"
  - websocket: "/ws for real-time processing updates"
  - cors: "Allow frontend origin for development"
  
FRONTEND:
  - state: "Zustand for global state management"  
  - persistence: "localStorage + IndexedDB for offline capability"
  - animations: "60fps spring physics with GPU acceleration"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Backend validation
cd backend && ruff check --fix app/ && mypy app/
cd backend && black app/ && isort app/

# Frontend validation  
cd frontend && npm run lint -- --fix && npm run type-check
cd frontend && npm run format

# Expected: No errors. If errors, READ and fix systematically.
```

### Level 2: Unit Tests
```python
# Backend tests - test_orchestrator_agent.py
import pytest
from app.agents.orchestrator import OrchestratorAgent
from app.models.pydantic_models import UserInput, InputCategory

@pytest.mark.asyncio
async def test_task_categorization():
    """Test AI categorizes tasks correctly"""
    agent = OrchestratorAgent()
    input_data = UserInput(text="Do my biology homework")
    
    result = await agent.categorize_input(input_data)
    assert result.category == InputCategory.TASK
    assert result.confidence > 0.7
    assert "homework" in result.reasoning.lower()

@pytest.mark.asyncio 
async def test_event_categorization():
    """Test AI categorizes events correctly"""
    agent = OrchestratorAgent()
    input_data = UserInput(text="Math exam at 2PM tomorrow")
    
    result = await agent.categorize_input(input_data)
    assert result.category == InputCategory.EVENT
    assert result.confidence > 0.7

@pytest.mark.asyncio
async def test_visual_feedback_stream():
    """Test streaming visual feedback follows mermaid diagram"""
    agent = OrchestratorAgent()
    input_data = UserInput(text="What is photosynthesis?")
    
    steps = []
    async for step in agent.process_with_visual_feedback(input_data):
        steps.append(step['step'])
    
    # CRITICAL: Verify mermaid diagram sequence
    expected_sequence = ['analyzing', 'categorizing', 'question_identified', 'complete']
    assert steps == expected_sequence

# Frontend tests - HeroInputBar.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { HeroInputBar } from '../HeroInputBar';

test('persists input across page refresh', async () => {
  const mockOnSubmit = jest.fn();
  
  const { rerender } = render(<HeroInputBar onSubmit={mockOnSubmit} />);
  
  const input = screen.getByPlaceholderText(/enter anything/i);
  fireEvent.change(input, { target: { value: 'test input' } });
  
  // Simulate page refresh
  rerender(<HeroInputBar onSubmit={mockOnSubmit} />);
  
  await waitFor(() => {
    expect(input).toHaveValue('test input');
  });
});

test('shows processing animations in correct sequence', async () => {
  // Test mermaid diagram animation sequence
  const mockOnSubmit = jest.fn();
  render(<HeroInputBar onSubmit={mockOnSubmit} />);
  
  const input = screen.getByPlaceholderText(/enter anything/i);
  fireEvent.change(input, { target: { value: 'Do homework' } });
  fireEvent.submit(input.closest('form')!);
  
  // Verify animation sequence follows mermaid diagram
  await waitFor(() => screen.getByText('Analyzing input...'));
  await waitFor(() => screen.getByText('Categorizing...'));
  await waitFor(() => screen.getByText('Task identified!'));
});
```

```bash
# Run tests iteratively until passing:
cd backend && pytest tests/ -v --cov=app --cov-report=term-missing
cd frontend && npm test -- --coverage --watchAll=false

# If failing: Debug specific test, check mocks, verify API contracts
```

### Level 3: Integration Test with Docker
```bash
# Start full stack with Docker
docker-compose up -d --build

# Wait for services to be ready
curl -f http://localhost:8000/health || exit 1
curl -f http://localhost:3000 || exit 1

# Test complete workflow via Playwright
cd tests/e2e && python -m pytest test_workflow.py -v

# Expected workflow:
# 1. User enters "Do math homework due tomorrow" in hero bar
# 2. See "Analyzing input..." animation
# 3. See "Categorizing..." animation  
# 4. See "Task identified!" animation
# 5. See task added to smart task list with AI-generated category
# 6. Verify task persists after page refresh
```

### Level 4: Animation Performance Test
```python
# tests/e2e/test_animations.py - Performance validation
from playwright.async_api import async_playwright
import asyncio

async def test_60fps_animations():
    """Verify animations maintain 60fps performance"""
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        
        # Enable performance monitoring
        await page.goto('http://localhost:3000')
        
        # Start performance measurement
        await page.evaluate('() => performance.mark("animation-start")')
        
        # Trigger input processing with complex input
        await page.fill('[placeholder*="Enter anything"]', 'Complex task with multiple requirements and deadlines')
        await page.click('button[type="submit"]')
        
        # Wait for complete animation sequence
        await page.wait_for_selector('.success-animation', timeout=10000)
        
        # Measure performance
        metrics = await page.evaluate('''() => {
            performance.mark("animation-end");
            performance.measure("animation-duration", "animation-start", "animation-end");
            const measure = performance.getEntriesByName("animation-duration")[0];
            return {
                duration: measure.duration,
                // Check for frame drops
                frameDrops: performance.getEntriesByType("measure").filter(m => m.duration > 16.67).length
            };
        }''')
        
        # CRITICAL: Animations must be smooth (< 5% frame drops)
        assert metrics['frameDrops'] < metrics['duration'] / 16.67 * 0.05
        
        await browser.close()
```

## Final Validation Checklist
- [ ] All tests pass: `pytest backend/tests/ && npm test frontend/`
- [ ] No linting errors: `ruff check backend/ && npm run lint frontend/`
- [ ] No type errors: `mypy backend/ && npm run type-check frontend/`
- [ ] Docker builds and runs: `docker-compose up --build`
- [ ] Hero input categorizes correctly without keywords
- [ ] Visual animations follow mermaid diagram exactly  
- [ ] Tasks auto-categorize and display in smart lists
- [ ] Events/reminders sync with calendar properly
- [ ] AI questions route to appropriate search tools
- [ ] Smooth 60fps animations throughout
- [ ] State persists across page refreshes
- [ ] OpenRouter free models work with tool calling adaptation
- [ ] WebSocket real-time updates function correctly
- [ ] Semantic search with Ollama embeddings works
- [ ] Web search via LangSearch returns structured results
- [ ] All error cases handled gracefully with user feedback

---

## Anti-Patterns to Avoid
- ❌ Don't hardcode keywords for categorization - use pure AI decision making
- ❌ Don't skip animation steps from mermaid diagram - follow it exactly
- ❌ Don't use sync functions in async Mirascope context
- ❌ Don't ignore WebSocket connection failures - implement reconnection
- ❌ Don't skip GPU acceleration for Framer Motion - use transform3d
- ❌ Don't forget WAL mode for SQLite in concurrent context  
- ❌ Don't ignore response_model for non-tool-calling OpenRouter models
- ❌ Don't skip error boundaries in React components
- ❌ Don't hardcode animation durations - make them configurable
- ❌ Don't ignore accessibility in animations - provide reduced motion options

## Confidence Score: 9.5/10

**Extremely high confidence** due to:
- **Comprehensive research**: 30+ pages of fresh documentation scraped
- **Clear architecture**: Follows proven Mirascope agent patterns
- **Visual specification**: Exact mermaid diagram to follow
- **Technology fit**: All components work well together  
- **Production focus**: Docker, testing, error handling included
- **Performance optimization**: GPU acceleration, async patterns
- **Detailed validation**: 4-level testing approach with specific metrics

**Minor uncertainty (0.5 points)**: 
- OpenRouter free model behavior with response_models (documented workaround included)
- First-time Ollama model download in Docker (pull scripts included)

**Risk mitigation included**:
- Fallback patterns for tool calling issues
- Progressive enhancement for animations
- Comprehensive error handling throughout
- Docker health checks for service dependencies
