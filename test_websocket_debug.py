#!/usr/bin/env python3
"""
Test WebSocket message reception with debug logging.
"""

import asyncio
import websockets
import json

async def test_message_debugging():
    """Send messages and see exactly what the backend receives."""
    try:
        uri = 'ws://localhost:8000/ws'
        async with websockets.connect(uri) as websocket:
            print('✅ WebSocket connected successfully!')
            
            # Send connection message first
            connect_message = {
                'type': 'connect',
                'payload': {
                    'clientId': 'debug-test-client'
                }
            }
            print(f'📤 Sending connect message: {json.dumps(connect_message, indent=2)}')
            await websocket.send(json.dumps(connect_message))
            
            # Read connection response
            response = await websocket.recv()
            print(f'📥 Connect response: {response}')
            
            # Test different message formats to see which one works
            test_messages = [
                {
                    'type': 'process_input',
                    'content': 'Test message format 1'
                },
                {
                    'type': 'process_input',
                    'payload': {
                        'input': 'Test message format 2'
                    }
                },
                {
                    'type': 'user_input',
                    'content': 'Test message format 3'
                }
            ]
            
            for i, msg in enumerate(test_messages, 1):
                print(f'\n📤 Sending test message {i}: {json.dumps(msg, indent=2)}')
                await websocket.send(json.dumps(msg))
                
                # Wait a bit for processing
                await asyncio.sleep(2)
                
                # Try to read any responses
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=1)
                        print(f'📥 Response to message {i}: {response}')
                except asyncio.TimeoutError:
                    print(f'⏰ No response to message {i} within timeout')
                    break
                    
    except Exception as e:
        print(f'❌ Test failed: {e}')

if __name__ == '__main__':
    asyncio.run(test_message_debugging())
