import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry
import time

BASE_URL = "http://localhost:3000"
TIMEOUT = 30

def test_error_handling_and_retry_mechanisms():
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[500, 502, 503, 504],
        allowed_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
        raise_on_status=False,
        raise_on_redirect=True
    )
    adapter = HTTPAdapter(max_retries=retries)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    headers = {
        "Content-Type": "application/json"
    }

    # 1. Test invalid input to AI Orchestrator endpoint (assuming POST /api/orchestrator/input)
    invalid_payload = {"input_text": ""}  # empty input considered invalid

    try:
        response = session.post(
            f"{BASE_URL}/api/orchestrator/input",
            json=invalid_payload,
            headers=headers,
            timeout=TIMEOUT
        )
        # Expecting 400 or similar client error for invalid input
        assert response.status_code >= 400 and response.status_code < 500, \
            f"Expected client error for invalid input, got {response.status_code}"
        json_resp = response.json()
        assert any(k in json_resp for k in ("error", "message", "detail")), \
            "Expected error message in response for invalid input"
    except requests.RequestException as e:
        assert False, f"RequestException on invalid input test: {e}"

    # 2. Test API failure simulation - call a non-existing endpoint to simulate 404
    try:
        response = session.get(
            f"{BASE_URL}/api/non_existing_endpoint",
            headers=headers,
            timeout=TIMEOUT
        )
        assert response.status_code == 404, f"Expected 404 for non-existing endpoint, got {response.status_code}"
    except requests.RequestException as e:
        assert False, f"RequestException on non-existing endpoint test: {e}"

    # 3. Test connection issue and retry mechanism by connecting to an invalid port (simulate connection error)
    invalid_url = "http://localhost:9999/api/orchestrator/input"
    start_time = time.time()
    try:
        session.post(
            invalid_url,
            json={"input_text": "Test retry"},
            headers=headers,
            timeout=5
        )
        assert False, "Expected connection error but request succeeded"
    except requests.exceptions.ConnectionError:
        # Expected connection error, retry should have been attempted internally by session
        elapsed = time.time() - start_time
        # Since retries=3 with backoff_factor=1, total delay ~ (1 + 2 + 4) seconds = 7 seconds approx
        assert elapsed >= 5, "Retry mechanism did not wait as expected on connection error"
    except requests.exceptions.RetryError:
        # RetryError can be raised for too many retries, count as expected for connection errors
        elapsed = time.time() - start_time
        assert elapsed >= 5, "Retry mechanism did not wait as expected on connection error"
    except requests.RequestException as e:
        assert False, f"Unexpected exception on connection error test: {e}"

    # 4. Test retry on server error (simulate 500 error)
    # Assuming there is an endpoint /api/test/server_error that returns 500 for testing purposes
    # If not available, skip this part gracefully
    try:
        response = session.get(
            f"{BASE_URL}/api/test/server_error",
            headers=headers,
            timeout=TIMEOUT
        )
        # The retry adapter should retry 3 times before returning final response
        # So final status code should be 500
        assert response.status_code == 500, f"Expected 500 server error, got {response.status_code}"
    except requests.RequestException:
        # If endpoint does not exist or not implemented, pass silently
        pass

    # 5. Test graceful user feedback on invalid JSON payload (malformed JSON)
    # requests library handles JSON serialization, so simulate by sending invalid content-type with invalid body
    try:
        response = session.post(
            f"{BASE_URL}/api/orchestrator/input",
            data="{'input_text': 'missing quotes}",  # malformed JSON string
            headers={"Content-Type": "application/json"},
            timeout=TIMEOUT
        )
        # Expecting 400 or 422 Unprocessable Entity
        assert response.status_code in (400, 422), f"Expected 400 or 422 for malformed JSON, got {response.status_code}"
        json_resp = response.json()
        assert any(k in json_resp for k in ("error", "message", "detail")), "Expected error message for malformed JSON"
    except requests.RequestException as e:
        assert False, f"RequestException on malformed JSON test: {e}"

test_error_handling_and_retry_mechanisms()
