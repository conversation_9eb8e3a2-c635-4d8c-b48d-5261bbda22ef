import json
import time
from websocket import create_connection

BASE_WS_ENDPOINT = "ws://localhost:3000/ws/orchestrator"
TEST_INPUT_TEXT = "Schedule a meeting with the team next Monday at 10am"

EXPECTED_STEPS = [
    "Analyzing input...",
    "Categorizing input...",
    "Identification complete",
    "Processing complete"
]


def test_websocket_streaming_ai_processing_updates():
    reconnect_attempts = 0
    max_reconnect_attempts = 3
    received_steps = []
    last_step_index = -1
    reconnect_delay = 1

    while reconnect_attempts <= max_reconnect_attempts:
        try:
            ws = create_connection(BASE_WS_ENDPOINT, timeout=30)
            # Send initial message to start AI processing streaming
            ws.send(json.dumps({"input": TEST_INPUT_TEXT}))

            while True:
                message = ws.recv()
                if not message:
                    raise ConnectionError("No message received from WebSocket")

                data = json.loads(message)
                step = data.get("step")
                if step:
                    if step in EXPECTED_STEPS:
                        current_index = EXPECTED_STEPS.index(step)
                        assert current_index >= last_step_index, (
                            f"Step '{step}' received out of order. Last step index: {last_step_index}, current: {current_index}"
                        )
                        last_step_index = current_index
                        received_steps.append(step)

                    if step == "Processing complete":
                        ws.close()
                        # Validate all expected steps received
                        for expected_step in EXPECTED_STEPS:
                            assert expected_step in received_steps, f"Expected step '{expected_step}' not received in streaming updates"

                        indices = [EXPECTED_STEPS.index(s) for s in received_steps if s in EXPECTED_STEPS]
                        assert indices == sorted(indices), "Visual feedback steps are not in correct sequence"
                        return

                if data.get("error"):
                    ws.close()
                    raise RuntimeError(f"Error received from WebSocket: {data['error']}")

        except (ConnectionError, RuntimeError, Exception) as e:
            reconnect_attempts += 1
            if reconnect_attempts > max_reconnect_attempts:
                raise AssertionError(f"WebSocket connection failed after {max_reconnect_attempts} attempts: {e}")
            time.sleep(reconnect_delay)
            reconnect_delay *= 2  # Exponential backoff


if __name__ == "__main__":
    test_websocket_streaming_ai_processing_updates()
