import asyncio
import json
import websockets
import time

BASE_ENDPOINT = "ws://localhost:3000/ws/ai-orchestrator"
TIMEOUT = 30

async def test_websocket_streaming_ai_processing_updates():
    """
    Verify that the WebSocket API supports stable real-time streaming of AI processing updates,
    including reconnection logic on failures and correct sequence of visual feedback steps.
    """

    input_text = "Schedule a meeting with the design team next Monday at 10am"
    expected_visual_steps = [
        "Analyzing input...",
        "Categorizing input...",
        "Identification complete",
        "Processing complete"
    ]

    received_steps = []

    async def connect_and_listen():
        try:
            async with websockets.connect(BASE_ENDPOINT, ping_interval=10, ping_timeout=5) as websocket:
                # Send initial message with user input to start AI processing
                await websocket.send(json.dumps({"type": "start_processing", "payload": {"text": input_text}}))

                start_time = time.time()
                while True:
                    if time.time() - start_time > TIMEOUT:
                        raise TimeoutError("Timeout waiting for AI processing updates")

                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=5)
                    except asyncio.TimeoutError:
                        # No message received in 5 seconds, consider reconnecting
                        raise ConnectionError("No message received, connection might be lost")

                    data = json.loads(message)

                    # Expecting messages with a 'step' field indicating visual feedback step
                    step = data.get("step")
                    if step:
                        received_steps.append(step)

                    # Check if processing complete step received
                    if step == "Processing complete":
                        return True

        except (websockets.ConnectionClosed, ConnectionError):
            # Connection lost, attempt reconnection once
            return False

    # Try connect and listen, with one reconnection attempt on failure
    success = await connect_and_listen()
    if not success:
        # Wait briefly before reconnecting
        await asyncio.sleep(1)
        success = await connect_and_listen()

    assert success, "Failed to receive complete AI processing updates after reconnection attempt"

    # Validate the sequence of visual feedback steps is correct and in order
    # The received steps should contain at least the expected steps in correct order
    idx = 0
    for step in received_steps:
        if idx < len(expected_visual_steps) and step == expected_visual_steps[idx]:
            idx += 1
    assert idx == len(expected_visual_steps), f"Visual feedback steps sequence incorrect. Expected {expected_visual_steps}, got {received_steps}"

asyncio.run(test_websocket_streaming_ai_processing_updates())