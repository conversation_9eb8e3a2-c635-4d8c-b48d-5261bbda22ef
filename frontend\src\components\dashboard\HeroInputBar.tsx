import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { motion, AnimatePresence } from 'framer-motion'
import { Send, Loader2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MicOff } from 'lucide-react'
import { useAppStore } from '@/stores/appStore'
import { useAIOrchestrator } from '@/hooks/useAIOrchestrator'
import clsx from 'clsx'

/**
 * Form data interface
 */
interface HeroInputFormData {
  input: string
}

/**
 * Hero Input Bar - Main Feature Implementation following research/react-hook-form/page2-get-started.md
 * 
 * Features:
 * - React Hook Form integration for efficient input handling
 * - Real-time input handling with persistence
 * - Submission with loading states
 * - Integration with AI service
 * - Form validation and error handling
 * - Voice input support
 * - Auto-resize textarea
 */
const HeroInputBar: React.FC = () => {
  const { isProcessing, setProcessing } = useAppStore()
  const { processInput, isConnected } = useAIOrchestrator()
  const [isListening, setIsListening] = useState(false)
  const [lastInput, setLastInput] = useState('')

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    reset,
  } = useForm<HeroInputFormData>({
    mode: 'onChange',
    defaultValues: {
      input: ''
    }
  })

  const currentInput = watch('input')

  // Auto-resize textarea effect
  useEffect(() => {
    const textarea = document.getElementById('hero-input') as HTMLTextAreaElement
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`
    }
  }, [currentInput])

  // Listen for external input events (from example buttons)
  useEffect(() => {
    const handleExternalInput = (event: CustomEvent) => {
      setValue('input', event.detail)
    }

    window.addEventListener('setHeroInput', handleExternalInput as EventListener)
    return () => {
      window.removeEventListener('setHeroInput', handleExternalInput as EventListener)
    }
  }, [setValue])

  // Form submission handler
  const onSubmit = async (data: HeroInputFormData) => {
    if (!data.input.trim() || isProcessing || !isConnected) return

    try {
      setLastInput(data.input)
      setProcessing(true, 'Processing your input...', 10)
      
      // Process the input through AI orchestrator
      await processInput(data.input)
      
      // Clear the form after successful submission
      reset()
      
    } catch (error) {
      console.error('Failed to process input:', error)
    } finally {
      setProcessing(false)
    }
  }

  // Voice input handler
  const handleVoiceInput = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      alert('Speech recognition is not supported in this browser')
      return
    }

    if (isListening) {
      setIsListening(false)
      return
    }

    const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition
    const recognition = new SpeechRecognition()
    
    recognition.continuous = false
    recognition.interimResults = false
    recognition.lang = 'en-US'

    recognition.onstart = () => {
      setIsListening(true)
    }

    recognition.onresult = (event: Event) => {
      const speechEvent = event as any // SpeechRecognition events aren't standard DOM types
      const transcript = speechEvent.results[0][0].transcript
      setValue('input', transcript)
      setIsListening(false)
    }

    recognition.onerror = (event: Event) => {
      const speechEvent = event as any
      console.error('Speech recognition error:', speechEvent.error)
      setIsListening(false)
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognition.start()
  }

  // Keyboard shortcuts
  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault()
      handleSubmit(onSubmit)()
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full max-w-3xl mx-auto"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="relative">
        {/* Main input container */}
        <div className="relative">
          <motion.div
            whileFocus={{ scale: 1.02 }}
            className={clsx(
              'relative overflow-hidden rounded-2xl border-2 transition-all duration-300',
              'bg-background-secondary/80 backdrop-blur-lg',
              isConnected 
                ? 'border-border-focus shadow-glow hover:shadow-glow-lg' 
                : 'border-accent-red shadow-medium',
              errors.input && 'border-accent-red'
            )}
          >
            {/* Input field */}
            <div className="relative flex items-end gap-4 p-4">
              <div className="flex-1">
                <textarea
                  id="hero-input"
                  {...register('input', {
                    required: 'Please enter your request',
                    minLength: {
                      value: 2,
                      message: 'Please enter at least 2 characters'
                    },
                    maxLength: {
                      value: 1000,
                      message: 'Input is too long (max 1000 characters)'
                    }
                  })}
                  placeholder={
                    isConnected 
                      ? "Tell me anything... I'll categorize it into tasks, events, or questions and handle it intelligently."
                      : "Connecting to AI services..."
                  }
                  disabled={!isConnected || isProcessing}
                  onKeyDown={handleKeyDown}
                  className="w-full bg-transparent text-text-primary placeholder-text-muted resize-none border-none outline-none text-lg leading-relaxed min-h-[60px] max-h-[200px]"
                  rows={1}
                />
                
                {/* Character count */}
                <div className="flex justify-between items-center mt-2">
                  <div className="text-text-muted text-xs">
                    {isConnected ? 'Ctrl/Cmd + Enter to submit' : 'Reconnecting...'}
                  </div>
                  <div className="text-text-muted text-xs">
                    {currentInput.length}/1000
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex items-end gap-2">
                {/* Voice input button */}
                <motion.button
                  type="button"
                  onClick={handleVoiceInput}
                  disabled={!isConnected || isProcessing}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className={clsx(
                    'p-3 rounded-xl transition-all duration-200 border',
                    isListening 
                      ? 'bg-accent-red/10 border-accent-red text-accent-red animate-pulse' 
                      : 'bg-background-hover hover:bg-background-active border-border-primary text-text-secondary hover:text-text-primary',
                    (!isConnected || isProcessing) && 'opacity-50 cursor-not-allowed'
                  )}
                  title={isListening ? 'Stop listening' : 'Voice input'}
                >
                  {isListening ? (
                    <MicOff className="w-5 h-5" />
                  ) : (
                    <Mic className="w-5 h-5" />
                  )}
                </motion.button>

                {/* Submit button */}
                <motion.button
                  type="submit"
                  disabled={!isValid || isProcessing || !isConnected || !currentInput.trim()}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={clsx(
                    'p-3 rounded-xl font-medium transition-all duration-200 flex items-center gap-2',
                    isValid && currentInput.trim() && isConnected && !isProcessing
                      ? 'bg-accent-blue hover:bg-accent-blue/90 text-white shadow-medium hover:shadow-glow'
                      : 'bg-background-hover border border-border-primary text-text-muted cursor-not-allowed'
                  )}
                  title={isProcessing ? 'Processing...' : 'Submit (Ctrl/Cmd + Enter)'}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span className="hidden sm:inline">Processing</span>
                    </>
                  ) : (
                    <>
                      <Send className="w-5 h-5" />
                      <span className="hidden sm:inline">Send</span>
                    </>
                  )}
                </motion.button>
              </div>
            </div>
          </motion.div>

          {/* Error message */}
          <AnimatePresence>
            {errors.input && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="mt-2 text-accent-red text-sm flex items-center gap-2"
              >
                <span>⚠️</span>
                {errors.input.message}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Connection status */}
          <AnimatePresence>
            {!isConnected && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="mt-2 text-accent-yellow text-sm flex items-center gap-2"
              >
                <div className="w-2 h-2 bg-accent-yellow rounded-full animate-pulse" />
                Reconnecting to AI services...
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Recent input indicator */}
        <AnimatePresence>
          {lastInput && !currentInput && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="mt-4 p-3 bg-background-tertiary/50 rounded-xl border border-border-primary"
            >
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <Sparkles className="w-4 h-4 text-accent-blue" />
                <span>Last processed: "{lastInput}"</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </form>

      {/* Pro tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="mt-6 text-center text-text-muted text-sm"
      >
        <div className="flex flex-wrap justify-center items-center gap-4">
          <span>💡 Pro tip: I understand natural language</span>
          <span>🎯 Be specific for better results</span>
          <span>🎤 Use voice input for convenience</span>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default HeroInputBar

// Extend the Window interface for speech recognition
declare global {
  interface Window {
    webkitSpeechRecognition: any
    SpeechRecognition: any
  }
}
