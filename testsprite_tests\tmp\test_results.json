[{"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "a7b124ca-dda3-424d-bb4e-6813335f6450", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC001-test_ai_orchestrator_input_categorization", "description": "Verify that the AI Orchestrator API correctly categorizes arbitrary user inputs into Task, Event, or AI Question with confidence scores above 0.7 without relying on keyword hardcoding.", "code": "import requests\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\n\ndef test_ai_orchestrator_input_categorization():\n    url = f\"{BASE_URL}/api/ai-orchestrator/categorize\"\n    headers = {\n        \"Content-Type\": \"application/json\"\n    }\n\n    # Arbitrary diverse inputs to test categorization without keyword hardcoding\n    test_inputs = [\n        \"Finish the quarterly report by next Monday\",\n        \"Schedule a meeting with the marketing team on Friday at 3pm\",\n        \"What is the weather forecast for New York tomorrow?\",\n        \"Remind me to call the dentist next week\",\n        \"How do I reset my password?\",\n        \"Plan a birthday party event for <PERSON>\",\n        \"Is the stock market going to rise this week?\",\n        \"Buy groceries after work\",\n        \"When is the next full moon?\",\n        \"Set an alarm for 7am tomorrow\"\n    ]\n\n    for user_input in test_inputs:\n        payload = {\"input_text\": user_input}\n        try:\n            response = requests.post(url, json=payload, headers=headers, timeout=TIMEOUT)\n        except requests.RequestException as e:\n            assert False, f\"Request failed for input '{user_input}': {e}\"\n\n        assert response.status_code == 200, f\"Unexpected status code {response.status_code} for input '{user_input}'\"\n\n        try:\n            data = response.json()\n        except ValueError:\n            assert False, f\"Response is not valid JSON for input '{user_input}'\"\n\n        # Expected response structure:\n        # {\n        #   \"category\": \"Task\" | \"Event\" | \"AI Question\",\n        #   \"confidence\": float (0.0 - 1.0),\n        #   \"reasoning\": \"string\"\n        # }\n\n        assert \"category\" in data, f\"'category' missing in response for input '{user_input}'\"\n        assert data[\"category\"] in [\"Task\", \"Event\", \"AI Question\"], f\"Invalid category '{data.get('category')}' for input '{user_input}'\"\n\n        assert \"confidence\" in data, f\"'confidence' missing in response for input '{user_input}'\"\n        confidence = data[\"confidence\"]\n        assert isinstance(confidence, (float, int)), f\"'confidence' is not a number for input '{user_input}'\"\n        assert 0.0 <= confidence <= 1.0, f\"'confidence' out of range for input '{user_input}'\"\n        assert confidence > 0.7, f\"Confidence {confidence} too low for input '{user_input}'\"\n\n        assert \"reasoning\" in data, f\"'reasoning' missing in response for input '{user_input}'\"\n        reasoning = data[\"reasoning\"]\n        assert isinstance(reasoning, str) and len(reasoning) > 0, f\"Invalid 'reasoning' for input '{user_input}'\"\n\ntest_ai_orchestrator_input_categorization()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 60, in <module>\n  File \"<string>\", line 33, in test_ai_orchestrator_input_categorization\nAssertionError: Unexpected status code 404 for input 'Finish the quarterly report by next Monday'\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.289Z", "modified": "2025-07-29T20:32:09.459Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "a290f0b4-0674-46a6-bc29-7c530addd34f", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC002-test_task_management_crud_operations", "description": "Validate the Task Management API endpoints for creating, reading, updating, and deleting tasks, ensuring AI-generated categories and priority color coding are correctly applied and persisted.", "code": "import requests\nimport uuid\n\nBASE_URL = \"http://localhost:3000\"\nHEADERS = {\"Content-Type\": \"application/json\"}\nTIMEOUT = 30\n\ndef test_task_management_crud_operations():\n    task_id = None\n    try:\n        # 1. Create a new task\n        create_payload = {\n            \"title\": \"Test Task \" + str(uuid.uuid4()),\n            \"description\": \"This is a test task for CRUD operations.\",\n            \"due_date\": \"2025-12-31T23:59:59Z\",\n            \"completed\": False\n        }\n        create_resp = requests.post(f\"{BASE_URL}/tasks\", json=create_payload, headers=HEADERS, timeout=TIMEOUT)\n        assert create_resp.status_code == 201, f\"Task creation failed: {create_resp.text}\"\n        created_task = create_resp.json()\n        task_id = created_task.get(\"id\")\n        assert task_id is not None, \"Created task ID is missing\"\n        # Validate AI-generated category and priority color coding presence\n        assert \"category\" in created_task, \"Task category missing in response\"\n        assert isinstance(created_task[\"category\"], str) and created_task[\"category\"], \"Invalid task category\"\n        assert \"priority_color\" in created_task, \"Priority color coding missing in response\"\n        assert isinstance(created_task[\"priority_color\"], str) and created_task[\"priority_color\"], \"Invalid priority color\"\n\n        # 2. Read the created task\n        read_resp = requests.get(f\"{BASE_URL}/tasks/{task_id}\", headers=HEADERS, timeout=TIMEOUT)\n        assert read_resp.status_code == 200, f\"Task retrieval failed: {read_resp.text}\"\n        read_task = read_resp.json()\n        assert read_task[\"id\"] == task_id, \"Retrieved task ID mismatch\"\n        assert read_task[\"title\"] == create_payload[\"title\"], \"Task title mismatch\"\n        assert read_task[\"description\"] == create_payload[\"description\"], \"Task description mismatch\"\n        assert read_task[\"completed\"] == create_payload[\"completed\"], \"Task completion status mismatch\"\n        assert read_task[\"category\"] == created_task[\"category\"], \"Task category mismatch\"\n        assert read_task[\"priority_color\"] == created_task[\"priority_color\"], \"Priority color mismatch\"\n\n        # 3. Update the task (change title, description, completed status)\n        update_payload = {\n            \"title\": create_payload[\"title\"] + \" - Updated\",\n            \"description\": create_payload[\"description\"] + \" Updated description.\",\n            \"completed\": True\n        }\n        update_resp = requests.put(f\"{BASE_URL}/tasks/{task_id}\", json=update_payload, headers=HEADERS, timeout=TIMEOUT)\n        assert update_resp.status_code == 200, f\"Task update failed: {update_resp.text}\"\n        updated_task = update_resp.json()\n        assert updated_task[\"title\"] == update_payload[\"title\"], \"Updated title mismatch\"\n        assert updated_task[\"description\"] == update_payload[\"description\"], \"Updated description mismatch\"\n        assert updated_task[\"completed\"] == update_payload[\"completed\"], \"Updated completion status mismatch\"\n        # Category and priority color should persist or be updated by AI - check presence and type\n        assert \"category\" in updated_task and isinstance(updated_task[\"category\"], str) and updated_task[\"category\"], \"Updated task category invalid\"\n        assert \"priority_color\" in updated_task and isinstance(updated_task[\"priority_color\"], str) and updated_task[\"priority_color\"], \"Updated priority color invalid\"\n\n        # 4. Delete the task\n        delete_resp = requests.delete(f\"{BASE_URL}/tasks/{task_id}\", headers=HEADERS, timeout=TIMEOUT)\n        assert delete_resp.status_code == 204, f\"Task deletion failed: {delete_resp.text}\"\n\n        # 5. Confirm deletion by attempting to read the task again\n        confirm_resp = requests.get(f\"{BASE_URL}/tasks/{task_id}\", headers=HEADERS, timeout=TIMEOUT)\n        assert confirm_resp.status_code == 404, \"Deleted task still accessible\"\n\n    except requests.RequestException as e:\n        assert False, f\"HTTP request failed: {e}\"\n    finally:\n        # Cleanup: If task still exists, delete it\n        if task_id:\n            try:\n                requests.delete(f\"{BASE_URL}/tasks/{task_id}\", headers=HEADERS, timeout=TIMEOUT)\n            except Exception:\n                pass\n\ntest_task_management_crud_operations()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 74, in <module>\n  File \"<string>\", line 19, in test_task_management_crud_operations\nAssertionError: Task creation failed: \n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.295Z", "modified": "2025-07-29T20:32:01.016Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "f9ff4752-8398-4cef-9a76-94f97e57dee4", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC003-test_calendar_event_management_and_conflict_detection", "description": "Test the Calendar API endpoints for event creation, viewing, updating, and deletion, including intelligent date parsing and conflict detection functionality.", "code": "import requests\nimport datetime\nimport time\n\nBASE_URL = \"http://localhost:3000/api\"\nHEADERS = {\"Content-Type\": \"application/json\"}\nTIMEOUT = 30\n\ndef test_calendar_event_management_and_conflict_detection():\n    # Helper functions\n    def create_event(event_data):\n        resp = requests.post(f\"{BASE_URL}/calendar/events\", json=event_data, headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n        return resp.json()\n\n    def get_event(event_id):\n        resp = requests.get(f\"{BASE_URL}/calendar/events/{event_id}\", headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n        return resp.json()\n\n    def update_event(event_id, update_data):\n        resp = requests.put(f\"{BASE_URL}/calendar/events/{event_id}\", json=update_data, headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n        return resp.json()\n\n    def delete_event(event_id):\n        resp = requests.delete(f\"{BASE_URL}/calendar/events/{event_id}\", headers=HEADERS, timeout=TIMEOUT)\n        if resp.status_code not in (200, 204):\n            resp.raise_for_status()\n\n    def list_events():\n        resp = requests.get(f\"{BASE_URL}/calendar/events\", headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n        return resp.json()\n\n    # Prepare event data with intelligent date parsing (ISO 8601 format)\n    now = datetime.datetime.utcnow()\n    start1 = (now + datetime.timedelta(hours=1)).isoformat() + \"Z\"\n    end1 = (now + datetime.timedelta(hours=2)).isoformat() + \"Z\"\n    event1 = {\n        \"title\": \"Test Event 1\",\n        \"description\": \"First test event\",\n        \"start_time\": start1,\n        \"end_time\": end1,\n        \"location\": \"Conference Room A\"\n    }\n\n    start2 = (now + datetime.timedelta(hours=1, minutes=30)).isoformat() + \"Z\"\n    end2 = (now + datetime.timedelta(hours=2, minutes=30)).isoformat() + \"Z\"\n    event2 = {\n        \"title\": \"Test Event 2\",\n        \"description\": \"Second test event overlapping first\",\n        \"start_time\": start2,\n        \"end_time\": end2,\n        \"location\": \"Conference Room B\"\n    }\n\n    created_event1 = None\n    created_event2 = None\n\n    try:\n        # Create first event\n        created_event1 = create_event(event1)\n        assert \"id\" in created_event1, \"Event creation response missing 'id'\"\n        assert created_event1[\"title\"] == event1[\"title\"]\n        assert created_event1[\"start_time\"] == event1[\"start_time\"]\n        assert created_event1[\"end_time\"] == event1[\"end_time\"]\n\n        # Create second event that conflicts with first event\n        created_event2 = create_event(event2)\n        assert \"id\" in created_event2, \"Event creation response missing 'id'\"\n        assert created_event2[\"title\"] == event2[\"title\"]\n\n        # Check conflict detection response or flag\n        # Assuming API returns a field \"conflicts\" listing conflicting event IDs\n        conflicts = created_event2.get(\"conflicts\", [])\n        assert created_event1[\"id\"] in conflicts, \"Conflict detection failed: overlapping event not detected\"\n\n        # Retrieve event1 and verify details\n        fetched_event1 = get_event(created_event1[\"id\"])\n        assert fetched_event1[\"id\"] == created_event1[\"id\"]\n        assert fetched_event1[\"title\"] == event1[\"title\"]\n\n        # Update event1's title and time to non-conflicting slot\n        new_start = (now + datetime.timedelta(hours=3)).isoformat() + \"Z\"\n        new_end = (now + datetime.timedelta(hours=4)).isoformat() + \"Z\"\n        update_data = {\n            \"title\": \"Updated Test Event 1\",\n            \"start_time\": new_start,\n            \"end_time\": new_end\n        }\n        updated_event1 = update_event(created_event1[\"id\"], update_data)\n        assert updated_event1[\"title\"] == update_data[\"title\"]\n        assert updated_event1[\"start_time\"] == update_data[\"start_time\"]\n        assert updated_event1[\"end_time\"] == update_data[\"end_time\"]\n\n        # Verify conflict is resolved after update\n        fetched_event2 = get_event(created_event2[\"id\"])\n        conflicts_after_update = fetched_event2.get(\"conflicts\", [])\n        assert created_event1[\"id\"] not in conflicts_after_update, \"Conflict not resolved after event update\"\n\n        # List all events and verify both exist\n        events_list = list_events()\n        event_ids = [e[\"id\"] for e in events_list]\n        assert created_event1[\"id\"] in event_ids\n        assert created_event2[\"id\"] in event_ids\n\n    finally:\n        # Cleanup created events\n        if created_event1:\n            try:\n                delete_event(created_event1[\"id\"])\n            except Exception:\n                pass\n        if created_event2:\n            try:\n                delete_event(created_event2[\"id\"])\n            except Exception:\n                pass\n\ntest_calendar_event_management_and_conflict_detection()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 121, in <module>\n  File \"<string>\", line 63, in test_calendar_event_management_and_conflict_detection\n  File \"<string>\", line 13, in create_event\n  File \"/var/task/requests/models.py\", line 1024, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:3000/api/calendar/events\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.300Z", "modified": "2025-07-29T20:33:11.929Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "af24dfc7-f7b6-443d-adc2-cf5feca37171", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC004-test_search_agent_routing_and_response_format", "description": "Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly, returning relevance-ranked results with source attribution and expandable answer panels.", "code": "import requests\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\nHEADERS = {\n    \"Content-Type\": \"application/json\",\n    \"Accept\": \"application/json\"\n}\n\ndef test_search_agent_routing_and_response_format():\n    \"\"\"\n    Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly,\n    returning relevance-ranked results with source attribution and expandable answer panels.\n    \"\"\"\n    # Example AI question input to trigger search agent routing\n    payload = {\n        \"query\": \"What are the latest advancements in AI-powered dashboards?\"\n    }\n\n    try:\n        response = requests.post(\n            f\"{BASE_URL}/api/search-agent/query\",\n            json=payload,\n            headers=HEADERS,\n            timeout=TIMEOUT\n        )\n        response.raise_for_status()\n    except requests.RequestException as e:\n        assert False, f\"Request to Search Agent API failed: {e}\"\n\n    data = response.json()\n\n    # Validate top-level response structure\n    assert isinstance(data, dict), \"Response should be a JSON object\"\n    assert \"results\" in data, \"Response missing 'results' key\"\n    assert isinstance(data[\"results\"], list), \"'results' should be a list\"\n    assert len(data[\"results\"]) > 0, \"'results' list should not be empty\"\n\n    # Validate each result item\n    for result in data[\"results\"]:\n        assert isinstance(result, dict), \"Each result should be a dictionary\"\n        # Required fields for relevance-ranked results with source attribution\n        assert \"title\" in result and isinstance(result[\"title\"], str) and result[\"title\"], \"Result missing valid 'title'\"\n        assert \"snippet\" in result and isinstance(result[\"snippet\"], str), \"Result missing valid 'snippet'\"\n        assert \"relevance_score\" in result and isinstance(result[\"relevance_score\"], (int, float)), \"Result missing valid 'relevance_score'\"\n        assert 0 <= result[\"relevance_score\"] <= 1, \"'relevance_score' must be between 0 and 1\"\n        assert \"source\" in result and isinstance(result[\"source\"], dict), \"Result missing valid 'source' attribution\"\n        # Source attribution should include at least a name and a URL\n        source = result[\"source\"]\n        assert \"name\" in source and isinstance(source[\"name\"], str) and source[\"name\"], \"Source missing valid 'name'\"\n        assert \"url\" in source and isinstance(source[\"url\"], str) and source[\"url\"].startswith(\"http\"), \"Source missing valid 'url'\"\n\n        # Check for expandable answer panel presence (optional field but if present must be string)\n        if \"answer_panel\" in result:\n            assert isinstance(result[\"answer_panel\"], str), \"'answer_panel' must be a string if present\"\n\n    # Validate that results are sorted by relevance_score descending\n    relevance_scores = [r[\"relevance_score\"] for r in data[\"results\"]]\n    assert relevance_scores == sorted(relevance_scores, reverse=True), \"Results are not sorted by relevance_score descending\"\n\ntest_search_agent_routing_and_response_format()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"<string>\", line 27, in test_search_agent_routing_and_response_format\n  File \"/var/task/requests/models.py\", line 1024, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:3000/api/search-agent/query\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 61, in <module>\n  File \"<string>\", line 29, in test_search_agent_routing_and_response_format\nAssertionError: Request to Search Agent API failed: 404 Client Error: Not Found for url: http://localhost:3000/api/search-agent/query\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.306Z", "modified": "2025-07-29T20:32:00.996Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "90062e46-9650-43e4-9094-5d52f56dce04", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC005-test_websocket_streaming_ai_processing_updates", "description": "Verify that the WebSocket API supports stable real-time streaming of AI processing updates, including reconnection logic on failures and correct sequence of visual feedback steps.", "code": "import asyncio\nimport json\nimport websockets\nimport time\n\nBASE_ENDPOINT = \"ws://localhost:3000/ws/ai-orchestrator\"\nTIMEOUT = 30\n\nasync def test_websocket_streaming_ai_processing_updates():\n    \"\"\"\n    Verify that the WebSocket API supports stable real-time streaming of AI processing updates,\n    including reconnection logic on failures and correct sequence of visual feedback steps.\n    \"\"\"\n\n    input_text = \"Schedule a meeting with the design team next Monday at 10am\"\n    expected_visual_steps = [\n        \"Analyzing input...\",\n        \"Categorizing input...\",\n        \"Identification complete\",\n        \"Processing complete\"\n    ]\n\n    received_steps = []\n\n    async def connect_and_listen():\n        try:\n            async with websockets.connect(BASE_ENDPOINT, ping_interval=10, ping_timeout=5) as websocket:\n                # Send initial message with user input to start AI processing\n                await websocket.send(json.dumps({\"type\": \"start_processing\", \"payload\": {\"text\": input_text}}))\n\n                start_time = time.time()\n                while True:\n                    if time.time() - start_time > TIMEOUT:\n                        raise TimeoutError(\"Timeout waiting for AI processing updates\")\n\n                    try:\n                        message = await asyncio.wait_for(websocket.recv(), timeout=5)\n                    except asyncio.TimeoutError:\n                        # No message received in 5 seconds, consider reconnecting\n                        raise ConnectionError(\"No message received, connection might be lost\")\n\n                    data = json.loads(message)\n\n                    # Expecting messages with a 'step' field indicating visual feedback step\n                    step = data.get(\"step\")\n                    if step:\n                        received_steps.append(step)\n\n                    # Check if processing complete step received\n                    if step == \"Processing complete\":\n                        return True\n\n        except (websockets.ConnectionClosed, ConnectionError):\n            # Connection lost, attempt reconnection once\n            return False\n\n    # Try connect and listen, with one reconnection attempt on failure\n    success = await connect_and_listen()\n    if not success:\n        # Wait briefly before reconnecting\n        await asyncio.sleep(1)\n        success = await connect_and_listen()\n\n    assert success, \"Failed to receive complete AI processing updates after reconnection attempt\"\n\n    # Validate the sequence of visual feedback steps is correct and in order\n    # The received steps should contain at least the expected steps in correct order\n    idx = 0\n    for step in received_steps:\n        if idx < len(expected_visual_steps) and step == expected_visual_steps[idx]:\n            idx += 1\n    assert idx == len(expected_visual_steps), f\"Visual feedback steps sequence incorrect. Expected {expected_visual_steps}, got {received_steps}\"\n\nasyncio.run(test_websocket_streaming_ai_processing_updates())", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 3, in <module>\nModuleNotFoundError: No module named 'websockets'\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.311Z", "modified": "2025-07-29T20:31:59.644Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "33fd5f9d-c972-45a3-92ab-c2ce8e968c7e", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC006-test_settings_management_api", "description": "Validate the Settings Management API endpoints for configuring API keys, model selection, and embedding index status, ensuring changes persist and reflect correctly in the system.", "code": "import requests\nimport uuid\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\nHEADERS = {\"Content-Type\": \"application/json\"}\n\ndef test_settings_management_api():\n    get_settings_url = f\"{BASE_URL}/settings\"\n    update_settings_url = f\"{BASE_URL}/settings\"\n\n    try:\n        resp = requests.get(get_settings_url, headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n        if not resp.content or resp.content.strip() == b'':\n            original_settings = {\"api_key\": \"\", \"model\": \"\", \"embedding_index_enabled\": False}\n        else:\n            try:\n                original_settings = resp.json()\n            except ValueError:\n                original_settings = {\"api_key\": \"\", \"model\": \"\", \"embedding_index_enabled\": False}\n    except Exception as e:\n        assert False, f\"Failed to get initial settings: {e}\"\n\n    # Basic validation of original settings keys\n    assert isinstance(original_settings, dict), \"Original settings response is not a dict\"\n    for key in [\"api_key\", \"model\", \"embedding_index_enabled\"]:\n        assert key in original_settings, f\"Key '{key}' missing in original settings\"\n\n    new_api_key = f\"test-api-key-{uuid.uuid4()}\"\n    new_model = \"gpt-4\"\n    new_embedding_status = not original_settings.get(\"embedding_index_enabled\", False)\n\n    updated_settings_payload = {\n        \"api_key\": new_api_key,\n        \"model\": new_model,\n        \"embedding_index_enabled\": new_embedding_status\n    }\n\n    try:\n        resp = requests.put(update_settings_url, json=updated_settings_payload, headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n        updated_settings = resp.json()\n    except Exception as e:\n        assert False, f\"Failed to update settings: {e}\"\n\n    assert updated_settings.get(\"api_key\") == new_api_key, \"API key was not updated correctly\"\n    assert updated_settings.get(\"model\") == new_model, \"Model was not updated correctly\"\n    assert updated_settings.get(\"embedding_index_enabled\") == new_embedding_status, \"Embedding index status was not updated correctly\"\n\n    try:\n        resp = requests.get(get_settings_url, headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n        persisted_settings = resp.json()\n    except Exception as e:\n        assert False, f\"Failed to get settings after update: {e}\"\n\n    assert persisted_settings.get(\"api_key\") == new_api_key, \"Persisted API key does not match updated value\"\n    assert persisted_settings.get(\"model\") == new_model, \"Persisted model does not match updated value\"\n    assert persisted_settings.get(\"embedding_index_enabled\") == new_embedding_status, \"Persisted embedding index status does not match updated value\"\n\n    try:\n        resp = requests.put(update_settings_url, json=original_settings, headers=HEADERS, timeout=TIMEOUT)\n        resp.raise_for_status()\n    except Exception as e:\n        print(f\"Warning: Failed to revert settings during cleanup: {e}\")\n\ntest_settings_management_api()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"<string>\", line 42, in test_settings_management_api\n  File \"/var/task/requests/models.py\", line 1024, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:3000/settings\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 68, in <module>\n  File \"<string>\", line 45, in test_settings_management_api\nAssertionError: Failed to update settings: 404 Client Error: Not Found for url: http://localhost:3000/settings\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.321Z", "modified": "2025-07-29T20:32:35.017Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "e662a9c6-9684-448a-b2c3-10d47d1841b8", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC007-test_error_handling_and_retry_mechanisms", "description": "Test the API endpoints for robust error handling and retry mechanisms, ensuring graceful user feedback is provided for invalid inputs, API failures, and connection issues.", "code": "import requests\nfrom requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Retry\nfrom requests.exceptions import RequestEx<PERSON>, ConnectionError, HTTPError, Timeout, RetryError\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\n\ndef test_error_handling_and_retry_mechanisms():\n    session = requests.Session()\n    retries = Retry(\n        total=3,\n        backoff_factor=1,\n        status_forcelist=[429, 500, 502, 503, 504],\n        allowed_methods=[\"GET\", \"POST\", \"PUT\", \"DELETE\", \"PATCH\"]\n    )\n    adapter = HTTPAdapter(max_retries=retries)\n    session.mount(\"http://\", adapter)\n    session.mount(\"https://\", adapter)\n\n    headers = {\n        \"Content-Type\": \"application/json\"\n    }\n\n    # 1. Test invalid input to AI Orchestrator endpoint (assuming POST /api/orchestrate)\n    invalid_payload = {\"input_text\": \"\"}  # empty input likely invalid\n\n    try:\n        response = session.post(\n            f\"{BASE_URL}/api/orchestrate\",\n            json=invalid_payload,\n            headers=headers,\n            timeout=TIMEOUT\n        )\n        # Expecting 400 or similar client error for invalid input\n        assert response.status_code >= 400 and response.status_code < 500, \\\n            f\"Expected client error for invalid input, got {response.status_code}\"\n        json_resp = response.json()\n        assert \"error\" in json_resp or \"message\" in json_resp or \"detail\" in json_resp, \\\n            \"Expected error message in response for invalid input\"\n    except (RequestException, ValueError) as e:\n        assert False, f\"Request failed or invalid JSON response for invalid input test: {e}\"\n\n    # 2. Test API failure simulation by calling a non-existent endpoint to trigger 404\n    try:\n        response = session.get(\n            f\"{BASE_URL}/api/nonexistent_endpoint\",\n            headers=headers,\n            timeout=TIMEOUT\n        )\n        assert response.status_code == 404, f\"Expected 404 for nonexistent endpoint, got {response.status_code}\"\n    except (RequestException, ValueError) as e:\n        assert False, f\"Request failed or invalid JSON response for nonexistent endpoint test: {e}\"\n\n    # 3. Test connection issue handling by connecting to an invalid port (simulate connection error)\n    invalid_url = \"http://localhost:9999/api/orchestrate\"\n    try:\n        session.get(invalid_url, timeout=5)\n        assert False, \"Expected connection error but request succeeded\"\n    except (ConnectionError, RetryError):\n        pass  # Expected connection error or retry error due to retries\n    except Exception as e:\n        assert False, f\"Unexpected exception type for connection error test: {e}\"\n\n    # 4. Test retry mechanism by simulating a server error endpoint (assuming /api/test-error returns 500)\n    # This endpoint may not exist; if it does not, we skip this part gracefully.\n    try:\n        response = session.get(\n            f\"{BASE_URL}/api/test-error\",\n            headers=headers,\n            timeout=TIMEOUT\n        )\n        # If endpoint exists, expect 500 or handled error after retries\n        if response.status_code >= 500:\n            # The retry mechanism should have retried 3 times before final failure\n            assert True\n        else:\n            # If no error, just pass\n            assert True\n    except HTTPError as e:\n        # If HTTPError raised after retries, acceptable\n        pass\n    except RequestException:\n        # Other request exceptions acceptable here\n        pass\n\n    # 5. Test graceful user feedback on invalid JSON payload (malformed JSON)\n    malformed_json = \"{input_text: 'Missing quotes'}\"  # invalid JSON string\n\n    try:\n        # Directly use requests.post with data param to send malformed JSON\n        response = session.post(\n            f\"{BASE_URL}/api/orchestrate\",\n            data=malformed_json,\n            headers={\"Content-Type\": \"application/json\"},\n            timeout=TIMEOUT\n        )\n        # Expecting 400 Bad Request or similar\n        assert response.status_code >= 400 and response.status_code < 500, \\\n            f\"Expected client error for malformed JSON, got {response.status_code}\"\n        json_resp = response.json()\n        assert \"error\" in json_resp or \"message\" in json_resp or \"detail\" in json_resp, \\\n            \"Expected error message in response for malformed JSON\"\n    except (RequestException, ValueError) as e:\n        assert False, f\"Request failed or invalid JSON response for malformed JSON test: {e}\"\n\ntest_error_handling_and_retry_mechanisms()\n", "testStatus": "PASSED", "testError": "", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.329Z", "modified": "2025-07-29T20:33:09.329Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "db5e25fd-a4cd-48f3-ae26-c8316f43c046", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC008-test_security_middleware_rate_limiting_and_input_validation", "description": "Verify that the security middleware correctly enforces rate limiting and input validation on all API endpoints to prevent abuse and injection attacks.", "code": "import requests\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nHEADERS = {\"Content-Type\": \"application/json\"}\nTIMEOUT = 30\n\ndef test_security_middleware_rate_limiting_and_input_validation():\n    \"\"\"\n    Verify that the security middleware correctly enforces rate limiting and input validation\n    on all API endpoints to prevent abuse and injection attacks.\n    \"\"\"\n\n    # Define a list of endpoints and methods to test rate limiting and input validation\n    # Based on PRD, main API endpoints likely include:\n    # - /api/orchestrator (POST) for AI input processing\n    # - /api/tasks (POST, GET)\n    # - /api/events (POST, GET)\n    # - /api/search (POST)\n    # - /api/settings (GET, PUT)\n    # We'll test a subset representative endpoints for rate limiting and input validation.\n\n    endpoints = [\n        {\"method\": \"POST\", \"url\": f\"{BASE_URL}/api/orchestrator\", \"payload\": {\"input_text\": \"Test input for AI categorization\"}},\n        {\"method\": \"POST\", \"url\": f\"{BASE_URL}/api/tasks\", \"payload\": {\"title\": \"Test Task\", \"description\": \"Task description\", \"priority\": \"medium\"}},\n        {\"method\": \"POST\", \"url\": f\"{BASE_URL}/api/events\", \"payload\": {\"title\": \"Test Event\", \"start_time\": \"2025-08-01T10:00:00Z\", \"end_time\": \"2025-08-01T11:00:00Z\"}},\n        {\"method\": \"POST\", \"url\": f\"{BASE_URL}/api/search\", \"payload\": {\"query\": \"What is AI?\"}},\n        {\"method\": \"PUT\", \"url\": f\"{BASE_URL}/api/settings\", \"payload\": {\"api_key\": \"validapikey123\", \"model\": \"gpt-4\"}},\n    ]\n\n    # 1. Test input validation with malicious payloads (SQL injection, script injection)\n    malicious_payloads = [\n        {\"input_text\": \"'; DROP TABLE users; --\"},\n        {\"title\": \"<script>alert('xss')</script>\", \"description\": \"desc\", \"priority\": \"high\"},\n        {\"title\": \"Event\", \"start_time\": \"2025-08-01T10:00:00Z\", \"end_time\": \"2025-08-01T11:00:00Z<script>\"},\n        {\"query\": \"' OR '1'='1\"},\n        {\"api_key\": \"validapikey123\", \"model\": \"<img src=x onerror=alert(1)>\"}\n    ]\n\n    for i, endpoint in enumerate(endpoints):\n        method = endpoint[\"method\"]\n        url = endpoint[\"url\"]\n        # Test with malicious payload\n        payload = malicious_payloads[i]\n        try:\n            if method == \"POST\":\n                resp = requests.post(url, json=payload, headers=HEADERS, timeout=TIMEOUT)\n            elif method == \"PUT\":\n                resp = requests.put(url, json=payload, headers=HEADERS, timeout=TIMEOUT)\n            else:\n                continue  # Skip unsupported methods here\n\n            # Expecting 4xx client error due to input validation failure\n            assert resp.status_code >= 400 and resp.status_code < 500, \\\n                f\"Input validation failed to reject malicious payload on {url}, status code: {resp.status_code}\"\n        except requests.RequestException as e:\n            assert False, f\"Request to {url} failed with exception: {e}\"\n\n    # 2. Test rate limiting by sending rapid repeated requests to one endpoint\n    rate_limit_url = f\"{BASE_URL}/api/orchestrator\"\n    valid_payload = {\"input_text\": \"Normal input for rate limit test\"}\n\n    # Send requests rapidly more than typical rate limit threshold (assumed 5 requests per second)\n    success_responses = 0\n    rate_limited_responses = 0\n    total_requests = 10\n\n    for _ in range(total_requests):\n        try:\n            resp = requests.post(rate_limit_url, json=valid_payload, headers=HEADERS, timeout=TIMEOUT)\n            if resp.status_code == 429:\n                rate_limited_responses += 1\n            elif 200 <= resp.status_code < 300:\n                success_responses += 1\n            else:\n                # Unexpected status code\n                assert False, f\"Unexpected status code {resp.status_code} during rate limit test\"\n        except requests.RequestException as e:\n            assert False, f\"Request failed during rate limit test with exception: {e}\"\n        time.sleep(0.1)  # 100ms between requests to trigger rate limiting if threshold is low\n\n    assert rate_limited_responses > 0, \"Rate limiting not enforced; no 429 responses received\"\n    assert success_responses > 0, \"No successful requests received before rate limiting triggered\"\n\ntest_security_middleware_rate_limiting_and_input_validation()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 85, in <module>\n  File \"<string>\", line 77, in test_security_middleware_rate_limiting_and_input_validation\nAssertionError: Unexpected status code 404 during rate limit test\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.335Z", "modified": "2025-07-29T20:32:12.326Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "428f63ce-7aa9-45af-9cc6-285d4bedcb4d", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC009-test_data_persistence_across_sessions", "description": "Ensure that data submitted through API endpoints persists reliably across page refreshes and sessions using SQLite with WAL mode and embedding storage.", "code": "import requests\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nHEADERS = {\"Content-Type\": \"application/json\"}\nTIMEOUT = 30\n\ndef test_data_persistence_across_sessions():\n    # Step 1: Submit arbitrary input to AI Orchestrator endpoint\n    input_payload = {\n        \"input_text\": \"Schedule a meeting with the team next Friday at 3pm\"\n    }\n    create_resp = None\n    try:\n        create_resp = requests.post(\n            f\"{BASE_URL}/api/orchestrator/process\",\n            json=input_payload,\n            headers=HEADERS,\n            timeout=TIMEOUT\n        )\n        assert create_resp.status_code == 200, f\"Unexpected status code: {create_resp.status_code}\"\n        create_data = create_resp.json()\n        # Validate response contains category and confidence > 0.7\n        assert \"category\" in create_data, \"Response missing 'category'\"\n        assert \"confidence\" in create_data, \"Response missing 'confidence'\"\n        assert create_data[\"confidence\"] > 0.7, f\"Confidence too low: {create_data['confidence']}\"\n        resource_id = create_data.get(\"resource_id\")\n        assert resource_id is not None, \"Response missing 'resource_id' for created resource\"\n\n        # Step 2: Simulate page refresh / new session by waiting and re-fetching the resource\n        time.sleep(2)  # simulate delay between sessions\n\n        # Step 3: Retrieve the created resource to verify persistence\n        get_resp = requests.get(\n            f\"{BASE_URL}/api/orchestrator/resource/{resource_id}\",\n            headers=HEADERS,\n            timeout=TIMEOUT\n        )\n        assert get_resp.status_code == 200, f\"Failed to retrieve resource: {get_resp.status_code}\"\n        get_data = get_resp.json()\n\n        # Validate the retrieved data matches the originally submitted data and AI categorization\n        assert get_data.get(\"input_text\") == input_payload[\"input_text\"], \"Input text mismatch on retrieval\"\n        assert get_data.get(\"category\") == create_data[\"category\"], \"Category mismatch on retrieval\"\n        assert abs(get_data.get(\"confidence\", 0) - create_data[\"confidence\"]) < 0.001, \"Confidence mismatch on retrieval\"\n\n        # Step 4: Simulate another session by re-fetching again after some time\n        time.sleep(2)\n        get_resp_2 = requests.get(\n            f\"{BASE_URL}/api/orchestrator/resource/{resource_id}\",\n            headers=HEADERS,\n            timeout=TIMEOUT\n        )\n        assert get_resp_2.status_code == 200, f\"Failed to retrieve resource second time: {get_resp_2.status_code}\"\n        get_data_2 = get_resp_2.json()\n\n        # Validate persistence again\n        assert get_data_2 == get_data, \"Data not persistent across sessions\"\n\n    finally:\n        # Cleanup: delete the created resource if possible\n        if create_resp is not None and create_resp.status_code == 200:\n            try:\n                requests.delete(\n                    f\"{BASE_URL}/api/orchestrator/resource/{resource_id}\",\n                    headers=HEADERS,\n                    timeout=TIMEOUT\n                )\n            except Exception:\n                pass\n\ntest_data_persistence_across_sessions()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 72, in <module>\n  File \"<string>\", line 21, in test_data_persistence_across_sessions\nAssertionError: Unexpected status code: 404\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.342Z", "modified": "2025-07-29T20:32:15.455Z"}, {"projectId": "4c3a58c8-d4a7-4f3f-9f1e-c71947d34469", "testId": "379c3cb0-4db6-41fc-9117-a8c2a8138611", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC010-test_performance_monitoring_api_responses", "description": "Validate that performance monitoring tools track API response times and resource usage accurately under typical workloads.", "code": "import requests\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\n\ndef test_performance_monitoring_api_responses():\n    \"\"\"\n    Validate that performance monitoring tools track API response times and resource usage accurately under typical workloads.\n    This test simulates typical workloads by making multiple requests to key API endpoints and measures response times.\n    It asserts that response times are within acceptable thresholds.\n    \"\"\"\n\n    # Define acceptable max response time in seconds for typical workloads\n    MAX_RESPONSE_TIME = 2.0\n\n    created_task_id = None\n    created_event_id = None\n\n    try:\n        # Test orchestrator input processing\n        r = requests.post(\n            f\"{BASE_URL}/api/orchestrator/process\",\n            json={\"input_text\": \"Schedule meeting with team tomorrow at 10am\"},\n            timeout=TIMEOUT,\n        )\n        assert r.status_code == 200, f\"Unexpected status code: {r.status_code}\"\n        data = r.json()\n        assert \"category\" in data, \"Response JSON missing 'category' field\"\n        assert \"confidence\" in data, \"Response JSON missing 'confidence' field\"\n        confidence_value = data.get(\"confidence\")\n        assert isinstance(confidence_value, (float, int)), \"Confidence must be a number\"\n        assert confidence_value >= 0.7, f\"Confidence too low: {confidence_value}\"\n\n        # Create a task to test CRUD and performance\n        start = time.perf_counter()\n        r = requests.post(\n            f\"{BASE_URL}/api/tasks\",\n            json={\"title\": \"Performance Test Task\", \"description\": \"Task for performance monitoring\", \"priority\": \"medium\"},\n            timeout=TIMEOUT,\n        )\n        elapsed = time.perf_counter() - start\n        assert r.status_code == 201, f\"Task creation failed with status: {r.status_code}\"\n        created_task = r.json()\n        created_task_id = created_task.get(\"id\")\n        assert created_task_id is not None, \"Created task missing 'id'\"\n        assert elapsed <= MAX_RESPONSE_TIME, f\"Task creation took too long: {elapsed}s\"\n\n        # Read tasks list\n        start = time.perf_counter()\n        r = requests.get(f\"{BASE_URL}/api/tasks\", timeout=TIMEOUT)\n        elapsed = time.perf_counter() - start\n        assert r.status_code == 200, f\"Fetching tasks failed with status: {r.status_code}\"\n        tasks = r.json()\n        assert isinstance(tasks, list), \"Tasks response is not a list\"\n        assert elapsed <= MAX_RESPONSE_TIME, f\"Fetching tasks took too long: {elapsed}s\"\n\n        # Create a calendar event\n        start = time.perf_counter()\n        r = requests.post(\n            f\"{BASE_URL}/api/events\",\n            json={\n                \"title\": \"Performance Test Event\",\n                \"start_time\": \"2025-08-01T09:00:00Z\",\n                \"end_time\": \"2025-08-01T10:00:00Z\",\n            },\n            timeout=TIMEOUT,\n        )\n        elapsed = time.perf_counter() - start\n        assert r.status_code == 201, f\"Event creation failed with status: {r.status_code}\"\n        created_event = r.json()\n        created_event_id = created_event.get(\"id\")\n        assert created_event_id is not None, \"Created event missing 'id'\"\n        assert elapsed <= MAX_RESPONSE_TIME, f\"Event creation took too long: {elapsed}s\"\n\n        # Read events list\n        start = time.perf_counter()\n        r = requests.get(f\"{BASE_URL}/api/events\", timeout=TIMEOUT)\n        elapsed = time.perf_counter() - start\n        assert r.status_code == 200, f\"Fetching events failed with status: {r.status_code}\"\n        events = r.json()\n        assert isinstance(events, list), \"Events response is not a list\"\n        assert elapsed <= MAX_RESPONSE_TIME, f\"Fetching events took too long: {elapsed}s\"\n\n        # Search agent query\n        start = time.perf_counter()\n        r = requests.post(\n            f\"{BASE_URL}/api/search\",\n            json={\"query\": \"What is the weather today?\"},\n            timeout=TIMEOUT,\n        )\n        elapsed = time.perf_counter() - start\n        assert r.status_code == 200, f\"Search query failed with status: {r.status_code}\"\n        search_results = r.json()\n        assert \"results\" in search_results, \"Search results missing 'results' field\"\n        assert elapsed <= MAX_RESPONSE_TIME, f\"Search query took too long: {elapsed}s\"\n\n        # Get settings\n        start = time.perf_counter()\n        r = requests.get(f\"{BASE_URL}/api/settings\", timeout=TIMEOUT)\n        elapsed = time.perf_counter() - start\n        assert r.status_code == 200, f\"Fetching settings failed with status: {r.status_code}\"\n        settings = r.json()\n        assert isinstance(settings, dict), \"Settings response is not a dict\"\n        assert elapsed <= MAX_RESPONSE_TIME, f\"Fetching settings took too long: {elapsed}s\"\n\n        # Update settings\n        start = time.perf_counter()\n        r = requests.put(\n            f\"{BASE_URL}/api/settings\",\n            json={\"api_key\": \"testkey123\", \"model\": \"gpt-4\", \"embedding_index_status\": \"ready\"},\n            timeout=TIMEOUT,\n        )\n        elapsed = time.perf_counter() - start\n        assert r.status_code in (200, 204), f\"Updating settings failed with status: {r.status_code}\"\n        assert elapsed <= MAX_RESPONSE_TIME, f\"Updating settings took too long: {elapsed}s\"\n\n    finally:\n        # Cleanup created task\n        if created_task_id:\n            try:\n                requests.delete(f\"{BASE_URL}/api/tasks/{created_task_id}\", timeout=TIMEOUT)\n            except Exception:\n                pass\n        # Cleanup created event\n        if created_event_id:\n            try:\n                requests.delete(f\"{BASE_URL}/api/events/{created_event_id}\", timeout=TIMEOUT)\n            except Exception:\n                pass\n\ntest_performance_monitoring_api_responses()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 132, in <module>\n  File \"<string>\", line 27, in test_performance_monitoring_api_responses\nAssertionError: Unexpected status code: 404\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:31:04.349Z", "modified": "2025-07-29T20:33:11.926Z"}]