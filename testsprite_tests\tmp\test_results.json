[{"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "8c8a5629-79fc-4318-babb-16ce1468edad", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC001-test_ai_orchestrator_input_categorization", "description": "Verify that the AI Orchestrator API correctly categorizes arbitrary user inputs into Task, Event, or AI Question with confidence scores above 0.7 without relying on keyword hardcoding.", "code": "import requests\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\n\ndef test_ai_orchestrator_input_categorization():\n    url = f\"{BASE_URL}/api/ai-orchestrator/categorize\"\n    headers = {\n        \"Content-Type\": \"application/json\"\n    }\n\n    # Arbitrary user inputs to test categorization without keyword hardcoding\n    test_inputs = [\n        \"Schedule a meeting with the design team next Thursday at 3pm\",\n        \"Remind me to buy groceries tomorrow\",\n        \"What is the capital of France?\",\n        \"Finish the quarterly report by Friday\",\n        \"Is it going to rain this weekend?\",\n        \"Plan a birthday party for <PERSON>\",\n        \"How do I reset my password?\",\n        \"Book a flight to New York on Monday\",\n        \"Tell me a joke\",\n        \"Add a dentist appointment on March 5th\"\n    ]\n\n    valid_categories = {\"Task\", \"Event\", \"AI Question\"}\n\n    for user_input in test_inputs:\n        payload = {\"input_text\": user_input}\n        try:\n            response = requests.post(url, json=payload, headers=headers, timeout=TIMEOUT)\n        except requests.RequestException as e:\n            assert False, f\"Request failed for input '{user_input}': {e}\"\n\n        assert response.status_code == 200, f\"Unexpected status code {response.status_code} for input '{user_input}'\"\n\n        try:\n            data = response.json()\n        except ValueError:\n            assert False, f\"Response is not valid JSON for input '{user_input}'\"\n\n        # Expected response schema:\n        # {\n        #   \"category\": \"Task\" | \"Event\" | \"AI Question\",\n        #   \"confidence\": float,\n        #   \"reasoning\": str (optional)\n        # }\n\n        category = data.get(\"category\")\n        confidence = data.get(\"confidence\")\n\n        assert category in valid_categories, f\"Invalid category '{category}' for input '{user_input}'\"\n        assert isinstance(confidence, (float, int)), f\"Confidence is not a number for input '{user_input}'\"\n        assert confidence > 0.7, f\"Confidence {confidence} too low for input '{user_input}'\"\n\ntest_ai_orchestrator_input_categorization()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 56, in <module>\n  File \"<string>\", line 35, in test_ai_orchestrator_input_categorization\nAssertionError: Unexpected status code 404 for input 'Schedule a meeting with the design team next Thursday at 3pm'\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.359Z", "modified": "2025-07-29T20:47:49.749Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "40814a30-95e6-4a03-9bd8-9d6655b0e6b3", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC002-test_task_management_crud_operations", "description": "Validate the Task Management API endpoints for creating, reading, updating, and deleting tasks, ensuring AI-generated categories and priority color coding are correctly applied and persisted.", "code": "import requests\nimport uuid\n\nBASE_URL = \"http://localhost:3000\"\nHEADERS = {\"Content-Type\": \"application/json\"}\nTIMEOUT = 30\n\n\ndef test_task_management_crud_operations():\n    task_id = None\n    try:\n        # 1. Create a new task\n        create_payload = {\n            \"title\": \"Test Task \" + str(uuid.uuid4()),\n            \"description\": \"This is a test task created for CRUD operations validation.\",\n            \"priority\": \"medium\"  # Assuming priority can be low, medium, high\n        }\n        # due_date is optional and should be omitted if None\n        due_date = None\n        if due_date is not None:\n            create_payload[\"due_date\"] = due_date\n\n        create_resp = requests.post(\n            f\"{BASE_URL}/tasks\",\n            json=create_payload,\n            headers=HEADERS,\n            timeout=TIMEOUT,\n        )\n        assert create_resp.status_code == 201, f\"Task creation failed: {create_resp.text}\"\n        created_task = create_resp.json()\n        task_id = created_task.get(\"id\")\n        assert task_id is not None, \"Created task ID is missing\"\n        # Validate AI-generated category and priority color coding presence\n        assert \"category\" in created_task, \"AI-generated category missing in created task\"\n        assert created_task[\"category\"] == \"Task\", \"Created task category is not 'Task'\"\n        assert \"priority_color\" in created_task, \"Priority color coding missing in created task\"\n        assert created_task[\"priority_color\"] in [\"red\", \"yellow\", \"green\"], \"Invalid priority color coding\"\n\n        # 2. Read the created task\n        read_resp = requests.get(\n            f\"{BASE_URL}/tasks/{task_id}\",\n            headers=HEADERS,\n            timeout=TIMEOUT,\n        )\n        assert read_resp.status_code == 200, f\"Task read failed: {read_resp.text}\"\n        read_task = read_resp.json()\n        assert read_task[\"id\"] == task_id, \"Read task ID mismatch\"\n        assert read_task[\"title\"] == create_payload[\"title\"], \"Read task title mismatch\"\n        assert read_task[\"category\"] == \"Task\", \"Read task category mismatch\"\n        assert read_task[\"priority_color\"] == created_task[\"priority_color\"], \"Read task priority color mismatch\"\n\n        # 3. Update the task - change title, priority and status\n        update_payload = {\n            \"title\": create_payload[\"title\"] + \" - Updated\",\n            \"priority\": \"high\",\n            \"status\": \"in_progress\",\n        }\n        update_resp = requests.put(\n            f\"{BASE_URL}/tasks/{task_id}\",\n            json=update_payload,\n            headers=HEADERS,\n            timeout=TIMEOUT,\n        )\n        assert update_resp.status_code == 200, f\"Task update failed: {update_resp.text}\"\n        updated_task = update_resp.json()\n        assert updated_task[\"title\"] == update_payload[\"title\"], \"Updated task title mismatch\"\n        assert updated_task[\"status\"] == update_payload[\"status\"], \"Updated task status mismatch\"\n        # Priority color coding should reflect updated priority\n        assert \"priority_color\" in updated_task, \"Priority color missing after update\"\n        # Assuming priority_color changes accordingly, e.g. high -> red\n        priority_color_map = {\"low\": \"green\", \"medium\": \"yellow\", \"high\": \"red\"}\n        expected_color = priority_color_map.get(update_payload[\"priority\"])\n        assert updated_task[\"priority_color\"] == expected_color, \"Priority color coding incorrect after update\"\n\n        # 4. List tasks and verify the updated task is present with correct data\n        list_resp = requests.get(\n            f\"{BASE_URL}/tasks\",\n            headers=HEADERS,\n            timeout=TIMEOUT,\n        )\n        assert list_resp.status_code == 200, f\"Task list retrieval failed: {list_resp.text}\"\n        tasks_list = list_resp.json()\n        found = False\n        for task in tasks_list:\n            if task.get(\"id\") == task_id:\n                found = True\n                assert task[\"title\"] == update_payload[\"title\"], \"Listed task title mismatch\"\n                assert task[\"status\"] == update_payload[\"status\"], \"Listed task status mismatch\"\n                assert task[\"priority_color\"] == expected_color, \"Listed task priority color mismatch\"\n                assert task[\"category\"] == \"Task\", \"Listed task category mismatch\"\n                break\n        assert found, \"Updated task not found in task list\"\n\n    finally:\n        # 5. Delete the created task\n        if task_id:\n            del_resp = requests.delete(\n                f\"{BASE_URL}/tasks/{task_id}\",\n                headers=HEADERS,\n                timeout=TIMEOUT,\n            )\n            assert del_resp.status_code in [200, 204], f\"Task deletion failed: {del_resp.text}\"\n            # Verify deletion by attempting to get the task again\n            get_after_del_resp = requests.get(\n                f\"{BASE_URL}/tasks/{task_id}\",\n                headers=HEADERS,\n                timeout=TIMEOUT,\n            )\n            assert get_after_del_resp.status_code == 404, \"Deleted task still accessible\"\n\n\ntest_task_management_crud_operations()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 112, in <module>\n  File \"<string>\", line 29, in test_task_management_crud_operations\nAssertionError: Task creation failed: \n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.365Z", "modified": "2025-07-29T20:48:31.885Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "2968ec18-3048-47de-999d-297be4993935", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC003-test_calendar_event_management_and_conflict_detection", "description": "Test the Calendar API endpoints for event creation, viewing, updating, and deletion, including intelligent date parsing and conflict detection functionality.", "code": "import requests\nimport datetime\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nAPI_PREFIX = \"/api/calendar/events\"\nHEADERS = {\"Content-Type\": \"application/json\"}\nTIMEOUT = 30\n\ndef test_calendar_event_management_and_conflict_detection():\n    created_event_ids = []\n\n    def create_event(event_data):\n        response = requests.post(f\"{BASE_URL}{API_PREFIX}\", json=event_data, headers=HEADERS, timeout=TIMEOUT)\n        response.raise_for_status()\n        event = response.json()\n        created_event_ids.append(event[\"id\"])\n        return event\n\n    def get_event(event_id):\n        response = requests.get(f\"{BASE_URL}{API_PREFIX}/{event_id}\", headers=HEADERS, timeout=TIMEOUT)\n        response.raise_for_status()\n        return response.json()\n\n    def update_event(event_id, update_data):\n        response = requests.put(f\"{BASE_URL}{API_PREFIX}/{event_id}\", json=update_data, headers=HEADERS, timeout=TIMEOUT)\n        response.raise_for_status()\n        return response.json()\n\n    def delete_event(event_id):\n        response = requests.delete(f\"{BASE_URL}{API_PREFIX}/{event_id}\", headers=HEADERS, timeout=TIMEOUT)\n        if response.status_code not in (200, 204):\n            response.raise_for_status()\n\n    try:\n        # 1. Create an event with intelligent date parsing (natural language date)\n        event_payload_1 = {\n            \"title\": \"Team Meeting\",\n            \"description\": \"Discuss project updates\",\n            \"start\": \"2025-08-01T10:00:00Z\",\n            \"end\": \"2025-08-01T11:00:00Z\"\n        }\n        event1 = create_event(event_payload_1)\n        assert event1[\"title\"] == event_payload_1[\"title\"]\n        assert event1[\"description\"] == event_payload_1[\"description\"]\n        assert event1[\"start\"] == event_payload_1[\"start\"]\n        assert event1[\"end\"] == event_payload_1[\"end\"]\n\n        # 2. Create a second event that conflicts with the first event to test conflict detection\n        event_payload_2 = {\n            \"title\": \"Client Call\",\n            \"description\": \"Call with important client\",\n            \"start\": \"2025-08-01T10:30:00Z\",\n            \"end\": \"2025-08-01T11:30:00Z\"\n        }\n        conflict_response = requests.post(f\"{BASE_URL}{API_PREFIX}\", json=event_payload_2, headers=HEADERS, timeout=TIMEOUT)\n        if conflict_response.status_code == 409:\n            # Conflict detected as expected\n            conflict_data = conflict_response.json()\n            assert \"conflict\" in conflict_data[\"detail\"].lower() or \"overlap\" in conflict_data[\"detail\"].lower()\n        else:\n            # If no conflict status, assume event created and add to cleanup\n            conflict_response.raise_for_status()\n            event2 = conflict_response.json()\n            created_event_ids.append(event2[\"id\"])\n            # Verify overlap detection by querying conflicts endpoint if available\n            conflicts_check = requests.get(f\"{BASE_URL}{API_PREFIX}/{event2['id']}/conflicts\", headers=HEADERS, timeout=TIMEOUT)\n            if conflicts_check.status_code == 200:\n                conflicts = conflicts_check.json()\n                assert any(e[\"id\"] == event1[\"id\"] for e in conflicts)\n\n        # 3. Retrieve the first event and verify details\n        fetched_event1 = get_event(event1[\"id\"])\n        assert fetched_event1[\"id\"] == event1[\"id\"]\n        assert fetched_event1[\"title\"] == event1[\"title\"]\n\n        # 4. Update the first event's time to a non-conflicting slot\n        updated_start = \"2025-08-01T11:30:00Z\"\n        updated_end = \"2025-08-01T12:30:00Z\"\n        update_payload = {\n            \"title\": event1[\"title\"],\n            \"description\": event1[\"description\"],\n            \"start\": updated_start,\n            \"end\": updated_end\n        }\n        updated_event = update_event(event1[\"id\"], update_payload)\n        assert updated_event[\"start\"] == updated_start\n        assert updated_event[\"end\"] == updated_end\n\n        # 5. Verify conflict no longer exists after update if second event was created\n        if len(created_event_ids) > 1:\n            conflicts_check = requests.get(f\"{BASE_URL}{API_PREFIX}/{event1['id']}/conflicts\", headers=HEADERS, timeout=TIMEOUT)\n            if conflicts_check.status_code == 200:\n                conflicts = conflicts_check.json()\n                assert all(e[\"id\"] != created_event_ids[1] for e in conflicts)\n\n        # 6. Delete all created events and verify deletion\n    finally:\n        for eid in created_event_ids:\n            try:\n                delete_event(eid)\n            except Exception:\n                pass\n\ntest_calendar_event_management_and_conflict_detection()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 105, in <module>\n  File \"<string>\", line 43, in test_calendar_event_management_and_conflict_detection\n  File \"<string>\", line 15, in create_event\n  File \"/var/task/requests/models.py\", line 1024, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:3000/api/calendar/events\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.372Z", "modified": "2025-07-29T20:48:30.617Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "91bb52e3-c760-497a-b0ce-1533672d6f74", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC004-test_search_agent_routing_and_response_format", "description": "Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly, returning relevance-ranked results with source attribution and expandable answer panels.", "code": "import requests\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\nHEADERS = {\n    \"Content-Type\": \"application/json\"\n}\n\ndef test_search_agent_routing_and_response_format():\n    \"\"\"\n    Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly,\n    returning relevance-ranked results with source attribution and expandable answer panels.\n    \"\"\"\n    # Example AI question input to trigger search routing\n    payload = {\n        \"query\": \"What are the latest advancements in AI-powered dashboards?\"\n    }\n\n    try:\n        response = requests.post(\n            f\"{BASE_URL}/api/search-agent/search\",\n            json=payload,\n            headers=HEADERS,\n            timeout=TIMEOUT\n        )\n        response.raise_for_status()\n    except requests.RequestException as e:\n        assert False, f\"Request to Search Agent API failed: {e}\"\n\n    data = response.json()\n\n    # Validate response structure\n    assert isinstance(data, dict), \"Response should be a JSON object\"\n    assert \"results\" in data, \"Response must contain 'results' key\"\n    results = data[\"results\"]\n    assert isinstance(results, list), \"'results' should be a list\"\n    assert len(results) > 0, \"Results list should not be empty\"\n\n    # Validate each result item\n    for item in results:\n        assert isinstance(item, dict), \"Each result should be a dictionary\"\n        # Check required keys for relevance-ranked results with source attribution and expandable answer panels\n        assert \"title\" in item and isinstance(item[\"title\"], str) and item[\"title\"], \"Result must have a non-empty 'title'\"\n        assert \"snippet\" in item and isinstance(item[\"snippet\"], str), \"Result must have a 'snippet' string\"\n        assert \"source\" in item and isinstance(item[\"source\"], str) and item[\"source\"], \"Result must have a non-empty 'source'\"\n        assert \"relevance_score\" in item and isinstance(item[\"relevance_score\"], (float, int)), \"Result must have a numeric 'relevance_score'\"\n        assert 0 <= item[\"relevance_score\"] <= 1, \"'relevance_score' must be between 0 and 1\"\n        # Optional expandable answer panel content\n        if \"answer_panel\" in item:\n            assert isinstance(item[\"answer_panel\"], dict), \"'answer_panel' must be a dictionary if present\"\n            # Check for expected keys inside answer_panel if present\n            assert \"content\" in item[\"answer_panel\"], \"'answer_panel' must contain 'content' key\"\n            assert isinstance(item[\"answer_panel\"][\"content\"], str), \"'content' in answer_panel must be a string\"\n\n    # Validate results are sorted by relevance_score descending\n    relevance_scores = [item[\"relevance_score\"] for item in results]\n    assert relevance_scores == sorted(relevance_scores, reverse=True), \"Results must be sorted by relevance_score descending\"\n\ntest_search_agent_routing_and_response_format()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"<string>\", line 26, in test_search_agent_routing_and_response_format\n  File \"/var/task/requests/models.py\", line 1024, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:3000/api/search-agent/search\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 59, in <module>\n  File \"<string>\", line 28, in test_search_agent_routing_and_response_format\nAssertionError: Request to Search Agent API failed: 404 Client Error: Not Found for url: http://localhost:3000/api/search-agent/search\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.378Z", "modified": "2025-07-29T20:47:48.256Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "93218e12-ea17-4fb3-b5bf-8aefb2a67479", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC005-test_websocket_streaming_ai_processing_updates", "description": "Verify that the WebSocket API supports stable real-time streaming of AI processing updates, including reconnection logic on failures and correct sequence of visual feedback steps.", "code": "import json\nimport time\nfrom websocket import create_connection\n\nBASE_WS_ENDPOINT = \"ws://localhost:3000/ws/orchestrator\"\nTEST_INPUT_TEXT = \"Schedule a meeting with the team next Monday at 10am\"\n\nEXPECTED_STEPS = [\n    \"Analyzing input...\",\n    \"Categorizing input...\",\n    \"Identification complete\",\n    \"Processing complete\"\n]\n\n\ndef test_websocket_streaming_ai_processing_updates():\n    reconnect_attempts = 0\n    max_reconnect_attempts = 3\n    received_steps = []\n    last_step_index = -1\n    reconnect_delay = 1\n\n    while reconnect_attempts <= max_reconnect_attempts:\n        try:\n            ws = create_connection(BASE_WS_ENDPOINT, timeout=30)\n            # Send initial message to start AI processing streaming\n            ws.send(json.dumps({\"input\": TEST_INPUT_TEXT}))\n\n            while True:\n                message = ws.recv()\n                if not message:\n                    raise ConnectionError(\"No message received from WebSocket\")\n\n                data = json.loads(message)\n                step = data.get(\"step\")\n                if step:\n                    if step in EXPECTED_STEPS:\n                        current_index = EXPECTED_STEPS.index(step)\n                        assert current_index >= last_step_index, (\n                            f\"Step '{step}' received out of order. Last step index: {last_step_index}, current: {current_index}\"\n                        )\n                        last_step_index = current_index\n                        received_steps.append(step)\n\n                    if step == \"Processing complete\":\n                        ws.close()\n                        # Validate all expected steps received\n                        for expected_step in EXPECTED_STEPS:\n                            assert expected_step in received_steps, f\"Expected step '{expected_step}' not received in streaming updates\"\n\n                        indices = [EXPECTED_STEPS.index(s) for s in received_steps if s in EXPECTED_STEPS]\n                        assert indices == sorted(indices), \"Visual feedback steps are not in correct sequence\"\n                        return\n\n                if data.get(\"error\"):\n                    ws.close()\n                    raise RuntimeError(f\"Error received from WebSocket: {data['error']}\")\n\n        except (ConnectionError, RuntimeError, Exception) as e:\n            reconnect_attempts += 1\n            if reconnect_attempts > max_reconnect_attempts:\n                raise AssertionError(f\"WebSocket connection failed after {max_reconnect_attempts} attempts: {e}\")\n            time.sleep(reconnect_delay)\n            reconnect_delay *= 2  # Exponential backoff\n\n\nif __name__ == \"__main__\":\n    test_websocket_streaming_ai_processing_updates()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 3, in <module>\nModuleNotFoundError: No module named 'websocket'\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.384Z", "modified": "2025-07-29T20:48:27.956Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "c3734a62-7df1-48d5-ba56-802f7b69efd5", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC006-test_settings_management_api", "description": "Validate the Settings Management API endpoints for configuring API keys, model selection, and embedding index status, ensuring changes persist and reflect correctly in the system.", "code": "import requests\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\nHEADERS = {\"Content-Type\": \"application/json\"}\n\ndef test_settings_management_api():\n    # Define endpoints\n    api_key_endpoint = f\"{BASE_URL}/settings/api-key\"\n    model_endpoint = f\"{BASE_URL}/settings/model\"\n    embedding_status_endpoint = f\"{BASE_URL}/settings/embedding-status\"\n\n    # Sample test data\n    new_api_key = {\"api_key\": \"test-api-key-123456\"}\n    new_model = {\"model_name\": \"gpt-4-test-model\"}\n    new_embedding_status = {\"enabled\": True}\n\n    # Store original settings to restore after test\n    original_api_key = None\n    original_model = None\n    original_embedding_status = None\n\n    try:\n        # 1. Get current API key\n        resp = requests.get(api_key_endpoint, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        if resp.content:\n            original_api_key = resp.json().get(\"api_key\")\n\n        # 2. Update API key\n        resp = requests.put(api_key_endpoint, json=new_api_key, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        assert resp.json().get(\"api_key\") == new_api_key[\"api_key\"]\n\n        # 3. Verify API key persisted by GET\n        resp = requests.get(api_key_endpoint, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        assert resp.json().get(\"api_key\") == new_api_key[\"api_key\"]\n\n        # 4. Get current model selection\n        resp = requests.get(model_endpoint, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        if resp.content:\n            original_model = resp.json().get(\"model_name\")\n\n        # 5. Update model selection\n        resp = requests.put(model_endpoint, json=new_model, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        assert resp.json().get(\"model_name\") == new_model[\"model_name\"]\n\n        # 6. Verify model selection persisted by GET\n        resp = requests.get(model_endpoint, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        assert resp.json().get(\"model_name\") == new_model[\"model_name\"]\n\n        # 7. Get current embedding index status\n        resp = requests.get(embedding_status_endpoint, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        if resp.content:\n            original_embedding_status = resp.json().get(\"enabled\")\n\n        # 8. Update embedding index status\n        resp = requests.put(embedding_status_endpoint, json=new_embedding_status, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        assert resp.json().get(\"enabled\") == new_embedding_status[\"enabled\"]\n\n        # 9. Verify embedding index status persisted by GET\n        resp = requests.get(embedding_status_endpoint, headers=HEADERS, timeout=TIMEOUT)\n        assert resp.status_code == 200\n        assert resp.json().get(\"enabled\") == new_embedding_status[\"enabled\"]\n\n    finally:\n        # Restore original API key\n        if original_api_key is not None:\n            try:\n                requests.put(api_key_endpoint, json={\"api_key\": original_api_key}, headers=HEADERS, timeout=TIMEOUT)\n            except Exception:\n                pass\n\n        # Restore original model\n        if original_model is not None:\n            try:\n                requests.put(model_endpoint, json={\"model_name\": original_model}, headers=HEADERS, timeout=TIMEOUT)\n            except Exception:\n                pass\n\n        # Restore original embedding status\n        if original_embedding_status is not None:\n            try:\n                requests.put(embedding_status_endpoint, json={\"enabled\": original_embedding_status}, headers=HEADERS, timeout=TIMEOUT)\n            except Exception:\n                pass\n\ntest_settings_management_api()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/requests/models.py\", line 974, in json\n    return complexjson.loads(self.text, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/var/lang/lib/python3.12/site-packages/simplejson/__init__.py\", line 514, in loads\n    return _default_decoder.decode(s)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/var/lang/lib/python3.12/site-packages/simplejson/decoder.py\", line 386, in decode\n    obj, end = self.raw_decode(s)\n               ^^^^^^^^^^^^^^^^^^\n  File \"/var/lang/lib/python3.12/site-packages/simplejson/decoder.py\", line 416, in raw_decode\n    return self.scan_once(s, idx=_w(s, idx).end())\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsimplejson.errors.JSONDecodeError: Expecting value: line 1 column 1 (char 0)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 94, in <module>\n  File \"<string>\", line 28, in test_settings_management_api\n  File \"/var/task/requests/models.py\", line 978, in json\n    raise RequestsJSONDecodeError(e.msg, e.doc, e.pos)\nrequests.exceptions.JSONDecodeError: Expecting value: line 1 column 1 (char 0)\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.390Z", "modified": "2025-07-29T20:48:27.972Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "673ea7e2-ae67-4c92-bfa7-3900a457fc77", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC007-test_error_handling_and_retry_mechanisms", "description": "Test the API endpoints for robust error handling and retry mechanisms, ensuring graceful user feedback is provided for invalid inputs, API failures, and connection issues.", "code": "import requests\nfrom requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\n\ndef test_error_handling_and_retry_mechanisms():\n    session = requests.Session()\n    retries = Retry(\n        total=3,\n        backoff_factor=1,\n        status_forcelist=[500, 502, 503, 504],\n        allowed_methods=[\"GET\", \"POST\", \"PUT\", \"DELETE\", \"PATCH\"],\n        raise_on_status=False,\n        raise_on_redirect=True\n    )\n    adapter = HTTPAdapter(max_retries=retries)\n    session.mount(\"http://\", adapter)\n    session.mount(\"https://\", adapter)\n\n    headers = {\n        \"Content-Type\": \"application/json\"\n    }\n\n    # 1. Test invalid input to AI Orchestrator endpoint (assuming POST /api/orchestrator/input)\n    invalid_payload = {\"input_text\": \"\"}  # empty input considered invalid\n\n    try:\n        response = session.post(\n            f\"{BASE_URL}/api/orchestrator/input\",\n            json=invalid_payload,\n            headers=headers,\n            timeout=TIMEOUT\n        )\n        # Expecting 400 or similar client error for invalid input\n        assert response.status_code >= 400 and response.status_code < 500, \\\n            f\"Expected client error for invalid input, got {response.status_code}\"\n        json_resp = response.json()\n        assert any(k in json_resp for k in (\"error\", \"message\", \"detail\")), \\\n            \"Expected error message in response for invalid input\"\n    except requests.RequestException as e:\n        assert False, f\"RequestException on invalid input test: {e}\"\n\n    # 2. Test API failure simulation - call a non-existing endpoint to simulate 404\n    try:\n        response = session.get(\n            f\"{BASE_URL}/api/non_existing_endpoint\",\n            headers=headers,\n            timeout=TIMEOUT\n        )\n        assert response.status_code == 404, f\"Expected 404 for non-existing endpoint, got {response.status_code}\"\n    except requests.RequestException as e:\n        assert False, f\"RequestException on non-existing endpoint test: {e}\"\n\n    # 3. Test connection issue and retry mechanism by connecting to an invalid port (simulate connection error)\n    invalid_url = \"http://localhost:9999/api/orchestrator/input\"\n    start_time = time.time()\n    try:\n        session.post(\n            invalid_url,\n            json={\"input_text\": \"Test retry\"},\n            headers=headers,\n            timeout=5\n        )\n        assert False, \"Expected connection error but request succeeded\"\n    except requests.exceptions.ConnectionError:\n        # Expected connection error, retry should have been attempted internally by session\n        elapsed = time.time() - start_time\n        # Since retries=3 with backoff_factor=1, total delay ~ (1 + 2 + 4) seconds = 7 seconds approx\n        assert elapsed >= 5, \"Retry mechanism did not wait as expected on connection error\"\n    except requests.exceptions.RetryError:\n        # RetryError can be raised for too many retries, count as expected for connection errors\n        elapsed = time.time() - start_time\n        assert elapsed >= 5, \"Retry mechanism did not wait as expected on connection error\"\n    except requests.RequestException as e:\n        assert False, f\"Unexpected exception on connection error test: {e}\"\n\n    # 4. Test retry on server error (simulate 500 error)\n    # Assuming there is an endpoint /api/test/server_error that returns 500 for testing purposes\n    # If not available, skip this part gracefully\n    try:\n        response = session.get(\n            f\"{BASE_URL}/api/test/server_error\",\n            headers=headers,\n            timeout=TIMEOUT\n        )\n        # The retry adapter should retry 3 times before returning final response\n        # So final status code should be 500\n        assert response.status_code == 500, f\"Expected 500 server error, got {response.status_code}\"\n    except requests.RequestException:\n        # If endpoint does not exist or not implemented, pass silently\n        pass\n\n    # 5. Test graceful user feedback on invalid JSON payload (malformed JSON)\n    # requests library handles JSON serialization, so simulate by sending invalid content-type with invalid body\n    try:\n        response = session.post(\n            f\"{BASE_URL}/api/orchestrator/input\",\n            data=\"{'input_text': 'missing quotes}\",  # malformed JSON string\n            headers={\"Content-Type\": \"application/json\"},\n            timeout=TIMEOUT\n        )\n        # Expecting 400 or 422 Unprocessable Entity\n        assert response.status_code in (400, 422), f\"Expected 400 or 422 for malformed JSON, got {response.status_code}\"\n        json_resp = response.json()\n        assert any(k in json_resp for k in (\"error\", \"message\", \"detail\")), \"Expected error message for malformed JSON\"\n    except requests.RequestException as e:\n        assert False, f\"RequestException on malformed JSON test: {e}\"\n\ntest_error_handling_and_retry_mechanisms()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 111, in <module>\n  File \"<string>\", line 66, in test_error_handling_and_retry_mechanisms\nAssertionError: Expected connection error but request succeeded\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.396Z", "modified": "2025-07-29T20:49:01.442Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "fb82a38d-90d2-4f3e-ab4f-a5e8c8b39ae5", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC008-test_security_middleware_rate_limiting_and_input_validation", "description": "Verify that the security middleware correctly enforces rate limiting and input validation on all API endpoints to prevent abuse and injection attacks.", "code": "import requests\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\nHEADERS = {\"Content-Type\": \"application/json\"}\n\ndef test_security_middleware_rate_limiting_and_input_validation():\n    # Endpoint to test - using AI Orchestrator input endpoint as representative for all endpoints\n    endpoint = f\"{BASE_URL}/api/orchestrator/input\"\n    valid_payload = {\"text\": \"Schedule a meeting tomorrow at 10am\"}\n    injection_payload = {\"text\": \"'; DROP TABLE users; --\"}\n    invalid_payload = {\"text\": \"\"}  # empty input to test validation\n\n    # 1. Test input validation: sending invalid payload (empty text)\n    response = requests.post(endpoint, json=invalid_payload, headers=HEADERS, timeout=TIMEOUT)\n    assert response.status_code == 400 or response.status_code == 422, \\\n        f\"Expected 400 or 422 for invalid input, got {response.status_code}\"\n    assert \"error\" in response.json() or \"detail\" in response.json(), \"Expected error message for invalid input\"\n\n    # 2. Test input validation: sending injection payload\n    response = requests.post(endpoint, json=injection_payload, headers=HEADERS, timeout=TIMEOUT)\n    # The system should sanitize or reject injection attempts\n    assert response.status_code in (200, 400, 422), f\"Unexpected status code for injection payload: {response.status_code}\"\n    # If accepted, ensure no harmful effect by checking response structure\n    if response.status_code == 200:\n        json_resp = response.json()\n        assert \"category\" in json_resp or \"result\" in json_resp, \"Expected valid response structure even for injection input\"\n\n    # 3. Test rate limiting: send multiple rapid requests to trigger rate limit\n    max_requests = 10\n    success_responses = 0\n    rate_limited = False\n    for i in range(max_requests + 5):\n        resp = requests.post(endpoint, json=valid_payload, headers=HEADERS, timeout=TIMEOUT)\n        if resp.status_code == 429:\n            rate_limited = True\n            break\n        elif resp.status_code == 200:\n            success_responses += 1\n        else:\n            # Unexpected status code\n            assert False, f\"Unexpected status code during rate limit test: {resp.status_code}\"\n\n    assert rate_limited, \"Rate limiting was not enforced after multiple rapid requests\"\n    assert success_responses > 0, \"No successful requests before rate limiting triggered\"\n\n    # 4. After waiting, rate limit should reset and allow requests again\n    time.sleep(2)  # wait 2 seconds (adjust if needed based on rate limit window)\n    resp = requests.post(endpoint, json=valid_payload, headers=HEADERS, timeout=TIMEOUT)\n    assert resp.status_code == 200, \"Expected successful request after rate limit reset\"\n\ntest_security_middleware_rate_limiting_and_input_validation()", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 53, in <module>\n  File \"<string>\", line 17, in test_security_middleware_rate_limiting_and_input_validation\nAssertionError: Expected 400 or 422 for invalid input, got 404\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.403Z", "modified": "2025-07-29T20:47:58.729Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "14c5501a-fa82-4307-a8a7-da487f2dfef3", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC009-test_data_persistence_across_sessions", "description": "Ensure that data submitted through API endpoints persists reliably across page refreshes and sessions using SQLite with WAL mode and embedding storage.", "code": "import requests\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\nHEADERS = {\"Content-Type\": \"application/json\"}\n\ndef test_data_persistence_across_sessions():\n    # Step 1: Submit arbitrary input to AI Orchestrator endpoint\n    input_payload = {\n        \"input_text\": \"Schedule a meeting with the team next Friday at 3pm\"\n    }\n    create_resp = requests.post(\n        f\"{BASE_URL}/api/orchestrator\",\n        json=input_payload,\n        headers=HEADERS,\n        timeout=TIMEOUT,\n    )\n    assert create_resp.status_code == 200, f\"Unexpected status code: {create_resp.status_code}\"\n    create_data = create_resp.json()\n    assert \"category\" in create_data, \"Response missing 'category'\"\n    assert create_data.get(\"confidence\", 0) > 0.7, \"Confidence score too low\"\n    resource_id = create_data.get(\"resource_id\")\n    assert resource_id, \"Response missing resource_id for created resource\"\n\n    # Determine correct resource path based on category\n    category = create_data.get(\"category\")\n    if category == \"Task\":\n        resource_path = \"tasks\"\n    elif category == \"Event\":\n        resource_path = \"events\"\n    elif category == \"AI Question\":\n        resource_path = \"questions\"\n    else:\n        raise AssertionError(f\"Unknown category returned: {category}\")\n\n    try:\n        # Step 2: Retrieve the created resource (task or event or question) to verify persistence\n        get_resp = requests.get(\n            f\"{BASE_URL}/api/{resource_path}/{resource_id}\",\n            headers=HEADERS,\n            timeout=TIMEOUT,\n        )\n        assert get_resp.status_code == 200, f\"Failed to retrieve resource {resource_id}\"\n        get_data = get_resp.json()\n        assert get_data.get(\"input_text\") == input_payload[\"input_text\"], \"Input text mismatch on retrieval\"\n        assert get_data.get(\"category\") == create_data.get(\"category\"), \"Category mismatch on retrieval\"\n\n        # Step 3: Simulate page refresh / new session by waiting and re-fetching the resource\n        time.sleep(2)  # simulate delay between sessions\n\n        get_resp_2 = requests.get(\n            f\"{BASE_URL}/api/{resource_path}/{resource_id}\",\n            headers=HEADERS,\n            timeout=TIMEOUT,\n        )\n        assert get_resp_2.status_code == 200, f\"Failed to retrieve resource on second fetch {resource_id}\"\n        get_data_2 = get_resp_2.json()\n        assert get_data_2 == get_data, \"Data mismatch between sessions, persistence failed\"\n\n        # Step 4: Verify that the backend SQLite WAL mode and embedding storage are implied by persistence\n        # (No direct API to check WAL mode, so persistence of data across sessions is the proxy)\n\n    finally:\n        # Cleanup: Delete the created resource to keep test environment clean\n        del_resp = requests.delete(\n            f\"{BASE_URL}/api/{resource_path}/{resource_id}\",\n            headers=HEADERS,\n            timeout=TIMEOUT,\n        )\n        assert del_resp.status_code in (200, 204), f\"Failed to delete resource {resource_id}\"\n\ntest_data_persistence_across_sessions()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 73, in <module>\n  File \"<string>\", line 19, in test_data_persistence_across_sessions\nAssertionError: Unexpected status code: 404\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.409Z", "modified": "2025-07-29T20:48:44.787Z"}, {"projectId": "ff436a07-8bad-485f-ae00-5b210d1a7f42", "testId": "06fbd93e-975c-44f5-a96d-601af567404c", "userId": "8458f438-4051-7067-ff66-cca1a8abd0f0", "title": "TC010-test_performance_monitoring_api_responses", "description": "Validate that performance monitoring tools track API response times and resource usage accurately under typical workloads.", "code": "import requests\nimport time\n\nBASE_URL = \"http://localhost:3000\"\nTIMEOUT = 30\n\ndef test_performance_monitoring_api_responses():\n    endpoints_to_test = [\n        {\"method\": \"GET\", \"url\": f\"{BASE_URL}/api/tasks\"},\n        {\"method\": \"POST\", \"url\": f\"{BASE_URL}/api/tasks\", \"json\": {\"title\": \"Performance Test Task\", \"description\": \"Task created for performance monitoring test\", \"priority\": \"medium\"}},\n        {\"method\": \"GET\", \"url\": f\"{BASE_URL}/api/events\"},\n        {\"method\": \"POST\", \"url\": f\"{BASE_URL}/api/events\", \"json\": {\"title\": \"Performance Test Event\", \"start_time\": \"2025-08-01T10:00:00Z\", \"end_time\": \"2025-08-01T11:00:00Z\"}},\n        {\"method\": \"POST\", \"url\": f\"{BASE_URL}/api/ai/orchestrate\", \"json\": {\"input\": \"Schedule a meeting tomorrow at 3pm\"}},\n        {\"method\": \"GET\", \"url\": f\"{BASE_URL}/api/settings\"},\n        {\"method\": \"PUT\", \"url\": f\"{BASE_URL}/api/settings\", \"json\": {\"api_key\": \"testkey\", \"model\": \"test-model\"}},\n        {\"method\": \"GET\", \"url\": f\"{BASE_URL}/api/search?q=AI performance\"},\n    ]\n\n    created_resources = []\n\n    try:\n        for ep in endpoints_to_test:\n            method = ep[\"method\"]\n            url = ep[\"url\"]\n            json_payload = ep.get(\"json\", None)\n\n            start_time = time.perf_counter()\n            try:\n                if method == \"GET\":\n                    response = requests.get(url, timeout=TIMEOUT)\n                elif method == \"POST\":\n                    response = requests.post(url, json=json_payload, timeout=TIMEOUT)\n                elif method == \"PUT\":\n                    response = requests.put(url, json=json_payload, timeout=TIMEOUT)\n                elif method == \"DELETE\":\n                    response = requests.delete(url, timeout=TIMEOUT)\n                else:\n                    raise ValueError(f\"Unsupported HTTP method: {method}\")\n            except requests.RequestException as e:\n                assert False, f\"Request to {url} failed with exception: {e}\"\n            end_time = time.perf_counter()\n\n            elapsed_ms = (end_time - start_time) * 1000\n\n            # Assert response status code is 2xx\n            assert 200 <= response.status_code < 300, f\"Unexpected status code {response.status_code} for {method} {url}\"\n\n            # Assert response time is reasonable (e.g., under 2000 ms)\n            assert elapsed_ms < 2000, f\"API response time too high: {elapsed_ms:.2f} ms for {method} {url}\"\n\n            # Optionally check resource usage headers if provided (e.g., X-Resource-Usage)\n            resource_usage = response.headers.get(\"X-Resource-Usage\")\n            if resource_usage:\n                # Example: header might be \"cpu=5%,mem=10MB\"\n                parts = resource_usage.split(\",\")\n                usage_dict = {}\n                for part in parts:\n                    if \"=\" in part:\n                        k, v = part.split(\"=\", 1)\n                        usage_dict[k.strip()] = v.strip()\n                # Basic sanity checks\n                if \"cpu\" in usage_dict:\n                    cpu_val = usage_dict[\"cpu\"].rstrip(\"%\")\n                    try:\n                        cpu_float = float(cpu_val)\n                        assert 0 <= cpu_float <= 100, f\"CPU usage out of range: {cpu_float}%\"\n                    except ValueError:\n                        assert False, f\"Invalid CPU usage value: {usage_dict['cpu']}\"\n                if \"mem\" in usage_dict:\n                    mem_val = usage_dict[\"mem\"].upper()\n                    # Just check it ends with MB or GB\n                    assert mem_val.endswith(\"MB\") or mem_val.endswith(\"GB\"), f\"Memory usage format unexpected: {mem_val}\"\n\n            # Track created resource IDs for cleanup\n            if method == \"POST\" and response.headers.get(\"Content-Type\", \"\").startswith(\"application/json\"):\n                try:\n                    data = response.json()\n                    if \"id\" in data:\n                        created_resources.append({\"url\": url, \"id\": data[\"id\"]})\n                except Exception:\n                    pass\n\n    finally:\n        # Cleanup created resources\n        for resource in created_resources:\n            try:\n                del_url = resource[\"url\"].rsplit(\"/\", 1)[0] + f\"/{resource['id']}\"\n                resp = requests.delete(del_url, timeout=TIMEOUT)\n                assert 200 <= resp.status_code < 300, f\"Failed to delete resource at {del_url}\"\n            except Exception:\n                pass\n\n\ntest_performance_monitoring_api_responses()\n", "testStatus": "FAILED", "testError": "Traceback (most recent call last):\n  File \"/var/task/handler.py\", line 258, in run_with_retry\n    exec(code, exec_env)\n  File \"<string>\", line 94, in <module>\n  File \"<string>\", line 46, in test_performance_monitoring_api_responses\nAssertionError: Unexpected status code 404 for GET http://localhost:3000/api/tasks\n", "testType": "BACKEND", "createFrom": "mcp", "created": "2025-07-29T20:47:09.415Z", "modified": "2025-07-29T20:49:23.028Z"}]