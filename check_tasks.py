#!/usr/bin/env python3
import sqlite3
import os

db_path = "./data/dashboard.db"
if not os.path.exists(db_path):
    print(f"Database not found at {db_path}")
    exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Check what tables exist
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("Available tables:")
for table in tables:
    print(f"  {table[0]}")

# Try to check tasks if table exists
table_names = [table[0] for table in tables]
if 'tasks' in table_names:
    print("\nRecent tasks:")
    cursor.execute("SELECT id, title, category, status, created_at FROM tasks ORDER BY id DESC LIMIT 5")
    results = cursor.fetchall()
    for row in results:
        print(f"ID: {row[0]}, Title: {row[1][:40]}..., Category: {row[2]}, Status: {row[3]}, Created: {row[4]}")
else:
    print("\nNo 'tasks' table found!")

conn.close()
