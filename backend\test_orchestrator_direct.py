#!/usr/bin/env python3
"""
Test AI orchestrator directly inside the container to isolate the issue.
"""

import asyncio
from app.agents.orchestrator import AIOrchestrator
from app.models.pydantic_models import UserInput

async def test_orchestrator_direct():
    """Test the orchestrator directly without WebSocket."""
    try:
        print('🧪 Testing AI orchestrator directly...')
        
        orchestrator = AIOrchestrator()
        user_input = UserInput(text='Create a task to test the AI categorization')
        
        print(f'📝 Input: {user_input.text}')
        print('🔄 Calling orchestrator.categorize_input...')
        
        result = await orchestrator.categorize_input(user_input)
        
        print(f'✅ Result: {result}')
        print(f'📊 Category: {result.category}')
        print(f'📊 Confidence: {result.confidence}')
        print(f'📊 Reasoning: {result.reasoning}')
        
        return True
        
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = asyncio.run(test_orchestrator_direct())
    if success:
        print('✅ AI orchestrator is working correctly!')
    else:
        print('❌ AI orchestrator has issues.')
