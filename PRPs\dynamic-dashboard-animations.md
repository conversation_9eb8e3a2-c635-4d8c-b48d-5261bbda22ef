# Dynamic Dashboard with Animated Input Processing & Multi-Type Workflows

## 📋 FILE INDEX - QUICK NAVIGATION
```
SECTION                                    LINES
================================================
📋 File Index & Navigation                1-22
🎯 Project Overview                        23-74
📖 Documentation & Context                 75-140
🏗️ Implementation Blueprint               141-381
🧪 Validation Loop                         382-423
🔧 Tools Available                         424-428
✅ Final Validation Checklist              430-441
❌ Anti-Patterns to Avoid                  442-449
📚 Detailed Implementation Examples        450-877
⚡ Performance Optimization                878-925
♿ Accessibility Considerations            926-964
🧪 Testing Strategy                        965-1048
```

## 🎯 PROJECT OVERVIEW

name: "Dynamic Dashboard with Animated Input Processing & Multi-Type Workflows"
description: |
  Implement a comprehensive dynamic dashboard system with smooth animations, visual feedback, and intelligent input processing workflows for Tasks, Events, and AI Questions. Features real-time visual transparency, animated state transitions, and adaptive UI components that respond to AI categorization results.

## 🎯 PURPOSE
Create a production-ready dashboard that provides complete visual transparency of AI processing steps through smooth animations, while implementing distinct workflows for different input types (Tasks, Events, AI Questions) with appropriate visual feedback and data display.

## 🔑 CORE PRINCIPLES
1. **Context is King**: Include ALL necessary documentation, examples, and caveats
2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
3. **Information Dense**: Use keywords and patterns from the codebase
4. **Progressive Success**: Start simple, validate, then enhance
5. **Global rules**: Follow all rules in CLAUDE.md and implement the mermaid diagram EXACTLY

---

## 🎯 GOAL
Build a dynamic dashboard system that follows the mermaid diagram logic with OneSearch-adapted animations:

### 🎯 MERMAID DIAGRAM COMPLIANCE (EXACT LOGIC)
- **Input Processing Flow**: User input → AI analysis → Categorization → Type-specific execution
- **Decision Tree Logic**: Exact branching for Task/Event/Question with "need more info" paths
- **Visual Step Representation**: Every step in the flowchart has animated visual feedback
- **Error Handling**: Proper fallback paths and user guidance as shown in flowchart

### 🎨 ANIMATION CONCEPTS (ADAPTED FROM DASHBOARD IDEA)
- **Morphing Input Bar**: Intelligent UX-focused morphing during processing stages
- **3-Stage Progression**: Input → Processing → Result with smooth animated transitions
- **Dynamic Component Updates**: Asynchronous updates to Calendar, TaskList, Recent Activity
- **Processing Animations**: Rich loading states that show AI thinking and categorization
- **Success State Feedback**: Clear visual confirmation adapted to OneSearch design
- **Pink Arrow Concept**: Text and UI elements animate/morph between states smoothly

## 💼 WHY
- **Business value**: Provides complete transparency in AI decision-making process
- **User impact**: Eliminates confusion about what AI is doing through visual feedback
- **Integration**: Unifies task management, calendar, and search in one animated interface
- **Innovation**: Creates engaging, transparent AI interaction patterns

## 📋 WHAT
A React-based dashboard where users input anything and see:
1. **Real-time categorization** with animated visual feedback
2. **Type-specific processing** with distinct animation sequences
3. **Adaptive result display** that morphs based on content type
4. **Smooth state transitions** between all processing phases
5. **Interactive result panels** with contextual actions

### ✅ SUCCESS CRITERIA (LOGIC + ADAPTED ANIMATIONS)
- [ ] **Mermaid Logic**: Input → Analysis → Categorization → Execution exactly as flowchart shows
- [ ] **Visual Step Feedback**: Every mermaid diagram step has animated visual representation
- [ ] **Morphing Input Bar**: UX-optimized morphing that enhances user understanding
- [ ] **3-Stage Progression**: Smooth animated transitions between input/processing/result states
- [ ] **Asynchronous Updates**: Calendar, TaskList, Recent Activity update dynamically and independently
- [ ] **Processing Animations**: Rich loading states showing AI analysis and categorization
- [ ] **Success Feedback**: Clear visual confirmation using OneSearch design language
- [ ] **Component Integration**: Works with existing components OR replaces them if better UX
- [ ] **OneSearch Branding**: Uses appropriate colors, fonts, and styling for the application
- [ ] **Performance**: 60fps animations with smooth transitions throughout entire flow

## 📖 ALL NEEDED CONTEXT

### 📚 DOCUMENTATION & REFERENCES
```yaml
# MUST READ - Include these in your context window
- docfile: research/framer-motion/page1-main.md
  why: Animation library setup and core concepts

- docfile: research/framer-motion/page3-animate.md
  why: Animation functions and spring physics for smooth 60fps effects

- docfile: research/framer-motion/page4-transitions.md
  why: Transition patterns and state-based animations

- docfile: research/react/page1-learn-react.md
  why: Modern React patterns for component state management

- docfile: research/react/page2-state-management.md
  why: State management for complex animation sequences

- docfile: research/tailwind/page1-main-docs.md
  why: Utility-first CSS for responsive animated components

- docfile: research/react-hook-form/page2-get-started.md
  why: Form handling with animation integration

- docfile: research/mirascope/page5-streams.md
  why: Real-time streaming for visual feedback

- docfile: research/langsearch/page1-web-search-api.md
  why: Web search integration for AI questions workflow
```

### 🔍 CURRENT CODEBASE ANALYSIS
```bash
# Key files to understand
frontend/src/components/dashboard/HeroInputBar.tsx  # Current input handling
frontend/src/pages/Dashboard.tsx                   # Main dashboard layout
frontend/src/hooks/useAIOrchestrator.ts           # AI processing logic
backend/app/main.py                               # WebSocket processing
backend/app/agents/orchestrator.py               # AI categorization
backend/app/tools/                               # Task, Calendar, Search tools
```

### ⚠️ KNOWN GOTCHAS & LIBRARY QUIRKS
```typescript
// CRITICAL: Framer Motion requires proper key props for list animations
// Example: Each animated item needs unique key for proper transitions
<AnimatePresence mode="wait">
  {items.map(item => (
    <motion.div key={item.id} layoutId={item.id}>
      {item.content}
    </motion.div>
  ))}
</AnimatePresence>

// CRITICAL: WebSocket state management with animations
// Must handle connection states properly to avoid animation glitches
const [connectionState, setConnectionState] = useState('connecting')
// Use connectionState to control animation states

// CRITICAL: Spring animations for 60fps performance
const springConfig = { type: "spring", stiffness: 300, damping: 30 }
// Use consistent spring configs across all animations
```

## 🏗️ IMPLEMENTATION BLUEPRINT

### Phase 1: Animation Infrastructure & Mermaid Logic (2.5 hours)
**Objective**: Build animation system following mermaid logic with OneSearch-adapted visuals

#### 1.1 Core Animation Engine Setup
- Install and configure Framer Motion for React with OneSearch theme integration
- Create animation configuration matching OneSearch design language
- Build reusable animation hooks for morphing, transitions, and loading states
- Set up 60fps performance monitoring and optimization tools

#### 1.2 Mermaid Diagram State Machine
- Create exact state machine following mermaid diagram decision tree
- Implement branching logic for Task/Event/Question/Need-More-Info paths
- Build animation triggers for each step in the flowchart
- Add error handling and fallback animations as shown in diagram

#### 1.3 Visual Step Representation System
- Create animated components for each mermaid diagram step
- Build morphing text system for processing state changes
- Implement visual feedback system that shows AI thinking process
- Design loading animations that represent categorization and analysis

### 📊 DATA MODELS AND STRUCTURE
```typescript
// Animation state management
interface AnimationState {
  phase: 'idle' | 'processing' | 'categorizing' | 'executing' | 'complete'
  inputType: 'task' | 'event' | 'question' | null
  progress: number
  currentStep: string
  error?: string
}

// Visual feedback states
interface VisualFeedback {
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  duration: number
  animation: 'fade' | 'slide' | 'bounce' | 'pulse'
}

// Result display configurations
interface ResultConfig {
  type: 'task' | 'event' | 'question'
  layout: 'card' | 'list' | 'calendar' | 'search'
  animations: AnimationSequence[]
}
```

### 📝 LIST OF TASKS (SEQUENTIAL IMPLEMENTATION)

```yaml
Task 1: Mermaid Diagram State Machine Implementation
MODIFY frontend/src/hooks/useAIOrchestrator.ts:
  - IMPLEMENT exact mermaid diagram decision tree logic
  - ADD visual step tracking for each flowchart node
  - INCLUDE animation triggers for state transitions
  - PRESERVE existing WebSocket functionality

CREATE frontend/src/hooks/useMermaidStateMachine.ts:
  - IMPLEMENT state machine following mermaid diagram exactly
  - ADD branching logic for Task/Event/Question/Need-More-Info
  - INCLUDE visual feedback triggers for each step
  - ADD error handling and fallback paths

Task 2: Morphing Input Bar with UX-Optimized Animations
MODIFY frontend/src/components/dashboard/HeroInputBar.tsx:
  - IMPLEMENT intelligent morphing during processing stages
  - ADD OneSearch-branded visual feedback system
  - INCLUDE smooth text transitions that enhance UX
  - PRESERVE existing form handling and validation

CREATE frontend/src/components/dashboard/MorphingInputFeedback.tsx:
  - IMPLEMENT UX-focused morphing animations
  - ADD processing state visualizations
  - INCLUDE categorization feedback with OneSearch styling

Task 3: Task Workflow with Asynchronous Component Updates
CREATE frontend/src/components/dashboard/TaskWorkflowAnimated.tsx:
  - IMPLEMENT task creation following mermaid diagram path
  - ADD asynchronous TaskList component updates
  - INCLUDE OneSearch-styled success feedback
  - INTEGRATE with existing or replace TaskList if better UX

MODIFY frontend/src/components/tasks/TaskList.tsx:
  - ADD dynamic animation support for new task additions
  - IMPLEMENT smooth entry animations for new tasks
  - INCLUDE real-time updates from dashboard workflow

Task 4: Event Workflow with Calendar Integration
CREATE frontend/src/components/dashboard/EventWorkflowAnimated.tsx:
  - IMPLEMENT event creation following mermaid diagram path
  - ADD asynchronous Calendar component updates
  - INCLUDE date/time selection with smooth animations
  - INTEGRATE with existing or replace Calendar if better UX

MODIFY frontend/src/components/calendar/Calendar.tsx:
  - ADD dynamic animation support for new event additions
  - IMPLEMENT smooth visual indicators for new events
  - INCLUDE real-time updates from dashboard workflow

Task 5: AI Question Workflow with Search Integration
CREATE frontend/src/components/dashboard/QuestionWorkflowAnimated.tsx:
  - IMPLEMENT AI question processing following mermaid diagram
  - ADD search progress animations with OneSearch branding
  - INCLUDE source discovery and result compilation feedback
  - INTEGRATE with existing search tools or replace if better UX

MODIFY frontend/src/components/search/SearchResults.tsx:
  - ADD dynamic animation support for search result updates
  - IMPLEMENT smooth result card animations
  - INCLUDE real-time updates from dashboard workflow

Task 6: 3-Stage Progression Animation System
CREATE frontend/src/components/dashboard/ThreeStageProgression.tsx:
  - IMPLEMENT input → processing → result stage transitions
  - ADD smooth morphing between stages with OneSearch styling
  - INCLUDE stage-specific visual feedback and animations
  - SUPPORT all workflow types (Task/Event/Question)

CREATE frontend/src/components/dashboard/StageTransitionManager.tsx:
  - IMPLEMENT stage transition logic and timing
  - ADD animation coordination between components
  - INCLUDE performance optimization for 60fps transitions

Task 7: Asynchronous Component Update System
CREATE frontend/src/components/dashboard/AsyncComponentManager.tsx:
  - IMPLEMENT independent component update animations
  - ADD asynchronous update coordination for Calendar, TaskList, Recent Activity
  - INCLUDE smooth visual transitions when components update independently
  - SUPPORT dynamic component replacement if better UX

CREATE frontend/src/hooks/useAsyncComponentUpdates.ts:
  - IMPLEMENT hook for managing asynchronous component updates
  - ADD animation triggers for independent component changes
  - INCLUDE state synchronization across components

Task 8: Enhanced Dashboard Layout with Animation Integration
MODIFY frontend/src/pages/Dashboard.tsx:
  - INTEGRATE all animated workflow components
  - ADD layout transitions that support the 3-stage progression
  - IMPLEMENT responsive behavior for animations
  - PRESERVE existing structure while enhancing with animations

CREATE frontend/src/components/dashboard/AnimatedDashboardLayout.tsx:
  - IMPLEMENT layout that supports morphing and transitions
  - ADD component positioning for optimal animation flow
  - INCLUDE responsive animation behavior

Task 9: Backend Mermaid Diagram Support
MODIFY backend/app/main.py:
  - ADD WebSocket events for each mermaid diagram step
  - IMPLEMENT detailed progress streaming for visual feedback
  - ADD step-by-step reporting that matches flowchart nodes
  - PRESERVE existing functionality

MODIFY backend/app/agents/orchestrator.py:
  - IMPLEMENT categorization logic that matches mermaid diagram exactly
  - ADD detailed step reporting for animation triggers
  - INCLUDE "need more info" path handling with visual feedback

Task 10: Processing Animation Library
CREATE frontend/src/components/animations/ProcessingAnimations.tsx:
  - IMPLEMENT rich loading states showing AI analysis
  - ADD categorization animations with OneSearch branding
  - INCLUDE morphing text animations for processing states
  - SUPPORT all processing types from mermaid diagram

CREATE frontend/src/components/animations/SuccessAnimations.tsx:
  - IMPLEMENT success state animations with OneSearch styling
  - ADD confirmation feedback for completed workflows
  - INCLUDE celebration animations for successful task/event creation

Task 11: Error Handling and Fallback Animations
CREATE frontend/src/components/dashboard/ErrorStateAnimations.tsx:
  - IMPLEMENT error state animations following mermaid diagram paths
  - ADD recovery animations and user guidance
  - INCLUDE "need more info" request animations
  - SUPPORT all error types with appropriate visual feedback

CREATE frontend/src/components/dashboard/FallbackAnimations.tsx:
  - IMPLEMENT fallback animations for network issues
  - ADD timeout handling with visual feedback
  - INCLUDE retry mechanisms with animated feedback

Task 12: Performance Optimization and Testing
CREATE tests/frontend/animation-performance.test.tsx:
  - TEST 60fps performance across all animations
  - VALIDATE smooth transitions and morphing effects
  - CHECK memory usage and animation cleanup
  - INCLUDE accessibility compliance tests

CREATE tests/playwright/mermaid-diagram-workflows.spec.ts:
  - TEST complete mermaid diagram flow execution
  - VALIDATE each step has proper visual representation
  - CHECK branching logic and error paths
  - INCLUDE cross-browser animation compatibility

Task 13: Recent Activity Animation Integration
MODIFY frontend/src/components/dashboard/RecentActivity.tsx:
  - ADD dynamic animation support for new activity entries
  - IMPLEMENT smooth entry animations for recent tasks/events/searches
  - INCLUDE real-time updates from workflow completions
  - INTEGRATE with existing or replace if better UX

CREATE frontend/src/components/dashboard/ActivityAnimations.tsx:
  - IMPLEMENT activity feed animations with OneSearch styling
  - ADD entry/exit animations for activity items
  - INCLUDE activity type-specific visual indicators

Task 14: Mobile and Responsive Animation Support
CREATE frontend/src/components/animations/ResponsiveAnimations.tsx:
  - IMPLEMENT responsive animation behavior for mobile devices
  - ADD touch-optimized animation interactions
  - INCLUDE performance optimizations for lower-end devices
  - SUPPORT all screen sizes with consistent animation quality

CREATE frontend/src/hooks/useResponsiveAnimations.ts:
  - IMPLEMENT hook for responsive animation management
  - ADD device detection and animation adaptation
  - INCLUDE performance monitoring for different devices

Task 15: Animation Configuration and Theming
CREATE frontend/src/config/animationConfig.ts:
  - IMPLEMENT centralized animation configuration
  - ADD OneSearch-specific timing and easing curves
  - INCLUDE animation theme integration
  - SUPPORT animation customization and preferences

CREATE frontend/src/themes/animationTheme.ts:
  - IMPLEMENT animation theme system integrated with OneSearch design
  - ADD consistent animation styling across all components
  - INCLUDE dark/light mode animation adaptations

Task 16: Advanced Morphing Effects
CREATE frontend/src/components/animations/MorphingEffects.tsx:
  - IMPLEMENT advanced morphing animations for UI elements
  - ADD smooth shape and size transitions
  - INCLUDE text morphing effects for processing states
  - SUPPORT complex morphing sequences

CREATE frontend/src/hooks/useMorphingTransitions.ts:
  - IMPLEMENT hook for managing morphing transitions
  - ADD morphing state management and timing
  - INCLUDE performance optimization for complex morphs

Task 17: Integration Testing and Validation
CREATE tests/integration/dashboard-animation-integration.test.tsx:
  - TEST integration between all animated components
  - VALIDATE workflow coordination and timing
  - CHECK state synchronization across animated elements
  - INCLUDE end-to-end animation flow testing

CREATE tests/performance/animation-performance-monitoring.test.ts:
  - IMPLEMENT performance monitoring for all animations
  - ADD frame rate validation and optimization
  - INCLUDE memory usage tracking for animations
  - SUPPORT continuous performance monitoring

Task 18: Documentation and Animation Guidelines
CREATE docs/animations/animation-system-guide.md:
  - DOCUMENT complete animation system architecture
  - ADD usage guidelines for developers
  - INCLUDE animation best practices and patterns
  - SUPPORT future animation development

CREATE docs/animations/mermaid-diagram-implementation.md:
  - DOCUMENT how mermaid diagram logic is implemented
  - ADD visual step mapping documentation
  - INCLUDE troubleshooting guide for animation issues
  - SUPPORT maintenance and updates
```

### 💻 PER TASK PSEUDOCODE

```typescript
// Task 1: Enhanced Animation State Management
// Pattern: Centralized state with animation coordination

interface AnimationState {
  phase: ProcessingPhase
  progress: number
  currentStep: string
  visualFeedback: VisualFeedback[]
  inputType: InputType | null
}

const useAnimationState = () => {
  const [state, setState] = useState<AnimationState>(initialState)
  
  const updatePhase = useCallback((phase: ProcessingPhase) => {
    setState(prev => ({
      ...prev,
      phase,
      progress: getProgressForPhase(phase)
    }))
  }, [])
  
  const addVisualFeedback = useCallback((feedback: VisualFeedback) => {
    setState(prev => ({
      ...prev,
      visualFeedback: [...prev.visualFeedback, feedback]
    }))
  }, [])
  
  return { state, updatePhase, addVisualFeedback }
}

// Task 2: Dynamic Input Bar with Visual Feedback
// Pattern: Morphing UI based on processing state

const AnimatedInputBar = () => {
  const { animationState } = useAnimationState()
  const springConfig = { type: "spring", stiffness: 300, damping: 30 }
  
  return (
    <motion.div
      animate={{
        scale: animationState.phase === 'processing' ? 1.02 : 1,
        borderColor: getBorderColorForPhase(animationState.phase)
      }}
      transition={springConfig}
    >
      <AnimatePresence mode="wait">
        {animationState.visualFeedback.map(feedback => (
          <motion.div
            key={feedback.id}
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
          >
            {feedback.message}
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  )
}
```

### 🔧 INTEGRATION POINTS
```yaml
WEBSOCKET:
  - event: "animation_step"
  - payload: { step: string, progress: number, type: string }
  
BACKEND_STREAMING:
  - endpoint: "/process-input"
  - streaming: true
  - animation_events: true
  
FRAMER_MOTION:
  - version: "^10.0.0"
  - features: ["AnimatePresence", "motion", "useAnimation"]
  
STATE_MANAGEMENT:
  - pattern: "Custom hooks with useCallback"
  - persistence: "sessionStorage for animation preferences"
```

## 🧪 VALIDATION LOOP

### Level 1: Animation Performance
```bash
# Test animation performance
npm run test:animations
npm run lighthouse:performance

# Expected: 60fps animations, <100ms response times
```

### Level 2: Workflow Testing
```typescript
// Test complete workflows
describe('Dashboard Workflows', () => {
  test('task creation workflow', async () => {
    // Test input → categorization → form → creation → success
    const result = await testTaskWorkflow("Create a meeting agenda")
    expect(result.animations).toHaveLength(5)
    expect(result.finalState).toBe('task_created')
  })
  
  test('event creation workflow', async () => {
    // Test input → categorization → calendar → creation → success
    const result = await testEventWorkflow("Schedule dentist appointment tomorrow")
    expect(result.animations).toHaveLength(6)
    expect(result.finalState).toBe('event_created')
  })
})
```

### Level 3: Integration Testing
```bash
# Start services
docker-compose up -d

# Run Playwright tests
npm run test:e2e:animations

# Expected: All workflows complete with proper animations
```

## 🔧 TOOLS AVAILABLE
- **Docker**: Read frontend/backend logs for debugging
- **Playwright MCP**: Test animations in real browser environment
- **Context7 MCP**: Research latest Framer Motion documentation
- **Jina scrape**: Get additional animation examples and patterns

## ✅ FINAL VALIDATION CHECKLIST
- [ ] All animations run at 60fps: `npm run test:performance`
- [ ] No animation glitches: `npm run test:animations`
- [ ] Responsive behavior works: `npm run test:responsive`
- [ ] WebSocket integration stable: `npm run test:websocket`
- [ ] Cross-browser compatibility: `npm run test:browsers`
- [ ] Accessibility maintained: `npm run test:a11y`
- [ ] Error states handle gracefully: `npm run test:errors`
- [ ] Loading states are smooth: `npm run test:loading`

---

## ❌ ANTI-PATTERNS TO AVOID
- ❌ Don't use CSS transitions for complex animations - use Framer Motion
- ❌ Don't animate layout properties without layoutId
- ❌ Don't forget AnimatePresence for enter/exit animations
- ❌ Don't use heavy animations on mobile devices
- ❌ Don't animate without proper loading states
- ❌ Don't ignore animation accessibility preferences

## 📚 DETAILED IMPLEMENTATION EXAMPLES

### Example 1: Animated Input Processing Flow
```typescript
// Complete implementation of animated input processing
const ProcessingFlow = () => {
  const [phase, setPhase] = useState<ProcessingPhase>('idle')
  const [progress, setProgress] = useState(0)
  const [inputType, setInputType] = useState<InputType | null>(null)

  const phaseVariants = {
    idle: { scale: 1, borderColor: "#e5e7eb" },
    processing: { scale: 1.02, borderColor: "#3b82f6" },
    categorizing: { scale: 1.02, borderColor: "#8b5cf6" },
    executing: { scale: 1.02, borderColor: "#10b981" },
    complete: { scale: 1, borderColor: "#059669" }
  }

  const progressVariants = {
    hidden: { width: 0 },
    visible: { width: `${progress}%` }
  }

  return (
    <motion.div
      className="relative overflow-hidden rounded-lg border-2"
      variants={phaseVariants}
      animate={phase}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* Progress bar */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 bg-blue-500"
        variants={progressVariants}
        animate="visible"
        transition={{ duration: 0.3 }}
      />

      {/* Phase indicator */}
      <AnimatePresence mode="wait">
        <motion.div
          key={phase}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          className="p-4"
        >
          {getPhaseMessage(phase, inputType)}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  )
}
```

### Example 2: Task Workflow Animation
```typescript
// Task creation workflow with form expansion
const TaskWorkflow = ({ input }: { input: string }) => {
  const [showForm, setShowForm] = useState(false)
  const [taskData, setTaskData] = useState<TaskData | null>(null)

  const containerVariants = {
    hidden: { height: 0, opacity: 0 },
    visible: {
      height: "auto",
      opacity: 1,
      transition: {
        height: { type: "spring", stiffness: 300, damping: 30 },
        opacity: { duration: 0.2 }
      }
    }
  }

  const formVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25,
        staggerChildren: 0.1
      }
    }
  }

  const fieldVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: { x: 0, opacity: 1 }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate={showForm ? "visible" : "hidden"}
      className="overflow-hidden"
    >
      <motion.form
        variants={formVariants}
        className="space-y-4 p-6 bg-white rounded-lg shadow-lg"
      >
        <motion.div variants={fieldVariants}>
          <label className="block text-sm font-medium text-gray-700">
            Task Title
          </label>
          <input
            type="text"
            defaultValue={extractTaskTitle(input)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          />
        </motion.div>

        <motion.div variants={fieldVariants}>
          <label className="block text-sm font-medium text-gray-700">
            Due Date
          </label>
          <input
            type="date"
            defaultValue={extractDueDate(input)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          />
        </motion.div>

        <motion.div variants={fieldVariants}>
          <label className="block text-sm font-medium text-gray-700">
            Priority
          </label>
          <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
          </select>
        </motion.div>

        <motion.div
          variants={fieldVariants}
          className="flex space-x-3"
        >
          <motion.button
            type="submit"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md"
          >
            Create Task
          </motion.button>
          <motion.button
            type="button"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowForm(false)}
            className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md"
          >
            Cancel
          </motion.button>
        </motion.div>
      </motion.form>
    </motion.div>
  )
}
```

### Example 3: Event Workflow with Calendar Animation
```typescript
// Event creation with animated calendar integration
const EventWorkflow = ({ input }: { input: string }) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [showCalendar, setShowCalendar] = useState(false)

  const calendarVariants = {
    hidden: {
      scale: 0.8,
      opacity: 0,
      y: -20
    },
    visible: {
      scale: 1,
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  }

  const dateVariants = {
    unselected: {
      scale: 1,
      backgroundColor: "#ffffff",
      color: "#374151"
    },
    selected: {
      scale: 1.1,
      backgroundColor: "#3b82f6",
      color: "#ffffff"
    },
    hover: {
      scale: 1.05,
      backgroundColor: "#dbeafe"
    }
  }

  return (
    <div className="space-y-4">
      {/* Event details form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-6 rounded-lg shadow-lg"
      >
        <h3 className="text-lg font-semibold mb-4">Create Event</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Event Title
            </label>
            <input
              type="text"
              defaultValue={extractEventTitle(input)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Date & Time
            </label>
            <motion.button
              onClick={() => setShowCalendar(!showCalendar)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="mt-1 w-full text-left px-3 py-2 border border-gray-300 rounded-md"
            >
              {selectedDate ? selectedDate.toLocaleDateString() : "Select date"}
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Animated calendar */}
      <AnimatePresence>
        {showCalendar && (
          <motion.div
            variants={calendarVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="bg-white p-6 rounded-lg shadow-lg"
          >
            <div className="grid grid-cols-7 gap-2">
              {generateCalendarDays().map((day, index) => (
                <motion.button
                  key={index}
                  variants={dateVariants}
                  initial="unselected"
                  animate={selectedDate?.getDate() === day.date ? "selected" : "unselected"}
                  whileHover="hover"
                  onClick={() => setSelectedDate(day.fullDate)}
                  className="p-2 rounded-md text-sm font-medium"
                >
                  {day.date}
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
```

### Example 4: AI Question Workflow with Search Animation
```typescript
// AI question processing with search result animations
const QuestionWorkflow = ({ input }: { input: string }) => {
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [currentStep, setCurrentStep] = useState<string>("")

  const searchSteps = [
    "Analyzing question...",
    "Searching web sources...",
    "Evaluating relevance...",
    "Compiling answer..."
  ]

  const resultVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  }

  const stepVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
  }

  return (
    <div className="space-y-6">
      {/* Search progress */}
      {isSearching && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 p-4 rounded-lg border border-blue-200"
        >
          <div className="flex items-center space-x-3">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"
            />
            <AnimatePresence mode="wait">
              <motion.span
                key={currentStep}
                variants={stepVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="text-blue-700 font-medium"
              >
                {currentStep}
              </motion.span>
            </AnimatePresence>
          </div>
        </motion.div>
      )}

      {/* Search results */}
      <AnimatePresence>
        {searchResults.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-4"
          >
            <h3 className="text-lg font-semibold text-gray-900">
              Search Results
            </h3>

            <motion.div
              className="space-y-3"
              variants={{
                visible: {
                  transition: {
                    staggerChildren: 0.1
                  }
                }
              }}
              initial="hidden"
              animate="visible"
            >
              {searchResults.map((result, index) => (
                <motion.div
                  key={result.id}
                  variants={resultVariants}
                  className="bg-white p-4 rounded-lg shadow-md border border-gray-200"
                >
                  <div className="flex items-start space-x-3">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.2 }}
                      className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
                    >
                      <span className="text-blue-600 font-semibold text-sm">
                        {index + 1}
                      </span>
                    </motion.div>

                    <div className="flex-1">
                      <motion.h4
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: index * 0.1 + 0.3 }}
                        className="font-medium text-gray-900 mb-1"
                      >
                        {result.title}
                      </motion.h4>

                      <motion.p
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: index * 0.1 + 0.4 }}
                        className="text-gray-600 text-sm mb-2"
                      >
                        {result.snippet}
                      </motion.p>

                      <motion.a
                        href={result.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="text-blue-600 text-sm hover:underline"
                      >
                        {result.domain}
                      </motion.a>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
```

## ⚡ PERFORMANCE OPTIMIZATION GUIDELINES

### Animation Performance Best Practices
```typescript
// Use transform properties for better performance
const optimizedVariants = {
  hidden: {
    opacity: 0,
    transform: "translateY(20px) scale(0.95)"
  },
  visible: {
    opacity: 1,
    transform: "translateY(0px) scale(1)"
  }
}

// Avoid animating layout properties
// ❌ Bad: animating width/height directly
// ✅ Good: using transform scale

// Use layoutId for shared element transitions
<motion.div layoutId="shared-element" />

// Optimize re-renders with useMemo
const memoizedVariants = useMemo(() => ({
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
}), [])
```

### Memory Management
```typescript
// Clean up animations on unmount
useEffect(() => {
  return () => {
    // Cancel any ongoing animations
    controls.stop()
  }
}, [controls])

// Use AnimatePresence properly
<AnimatePresence mode="wait" onExitComplete={() => {
  // Clean up after exit animation
}}>
  {/* Animated components */}
</AnimatePresence>
```

## ♿ ACCESSIBILITY CONSIDERATIONS

### Respecting User Preferences
```typescript
// Check for reduced motion preference
const prefersReducedMotion = useReducedMotion()

const variants = {
  hidden: { opacity: 0, y: prefersReducedMotion ? 0 : 20 },
  visible: { opacity: 1, y: 0 }
}

// Provide alternative feedback for reduced motion
{prefersReducedMotion ? (
  <div className="border-l-4 border-blue-500 pl-3">
    {statusMessage}
  </div>
) : (
  <motion.div variants={variants}>
    {statusMessage}
  </motion.div>
)}
```

### Focus Management
```typescript
// Maintain focus during animations
const handleAnimationComplete = () => {
  if (shouldFocusAfterAnimation) {
    nextFocusRef.current?.focus()
  }
}

<motion.div
  onAnimationComplete={handleAnimationComplete}
  // ... other props
/>
```

## 🧪 TESTING STRATEGY

### Animation Testing
```typescript
// Test animation states
describe('Dashboard Animations', () => {
  test('input processing shows correct phases', async () => {
    render(<DashboardWithAnimations />)

    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'Create a task' } })
    fireEvent.submit(input.closest('form'))

    // Check for processing phase
    await waitFor(() => {
      expect(screen.getByText(/processing/i)).toBeInTheDocument()
    })

    // Check for categorization phase
    await waitFor(() => {
      expect(screen.getByText(/categorizing/i)).toBeInTheDocument()
    })

    // Check for execution phase
    await waitFor(() => {
      expect(screen.getByText(/creating task/i)).toBeInTheDocument()
    })
  })

  test('respects reduced motion preference', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
      })),
    })

    render(<AnimatedComponent />)

    // Verify no motion animations are applied
    const animatedElement = screen.getByTestId('animated-element')
    expect(animatedElement).not.toHaveStyle('transform: translateY(20px)')
  })
})
```

### Performance Testing
```typescript
// Test animation performance
describe('Animation Performance', () => {
  test('maintains 60fps during complex animations', async () => {
    const performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        if (entry.entryType === 'measure') {
          expect(entry.duration).toBeLessThan(16.67) // 60fps threshold
        }
      })
    })

    performanceObserver.observe({ entryTypes: ['measure'] })

    render(<ComplexAnimatedDashboard />)

    // Trigger complex animation sequence
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'Complex input' } })

    await waitFor(() => {
      expect(screen.getByTestId('animation-complete')).toBeInTheDocument()
    })

    performanceObserver.disconnect()
  })
})
```

This comprehensive PRP provides everything needed to implement a production-ready dynamic dashboard with smooth animations, visual feedback, and multi-type input processing workflows. The implementation follows the mermaid diagram exactly while providing extensive documentation, examples, and testing strategies.
