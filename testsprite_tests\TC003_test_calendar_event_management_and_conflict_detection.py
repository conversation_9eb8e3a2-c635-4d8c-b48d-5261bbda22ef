import requests
import datetime
import time

BASE_URL = "http://localhost:3000"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30

def test_calendar_event_management_and_conflict_detection():
    # Helper functions
    def create_event(event_data):
        resp = requests.post(f"{BASE_URL}/api/events", json=event_data, headers=HEADERS, timeout=TIMEOUT)
        if resp.status_code != 200:
            raise Exception(f"Event creation failed: {resp.status_code} - {resp.text}")
        response_data = resp.json()
        if not response_data.get("success"):
            raise Exception(f"Event creation not successful: {response_data}")
        return response_data

    def get_events():
        resp = requests.get(f"{BASE_URL}/api/events", headers=HEADERS, timeout=TIMEOUT)
        if resp.status_code != 200:
            raise Exception(f"Event retrieval failed: {resp.status_code} - {resp.text}")
        response_data = resp.json()
        if not response_data.get("success"):
            raise Exception(f"Event retrieval not successful: {response_data}")
        return response_data.get("events", [])

    def update_event(event_id, update_data):
        update_data["event_id"] = event_id
        resp = requests.put(f"{BASE_URL}/api/events/{event_id}", json=update_data, headers=HEADERS, timeout=TIMEOUT)
        if resp.status_code != 200:
            raise Exception(f"Event update failed: {resp.status_code} - {resp.text}")
        response_data = resp.json()
        if not response_data.get("success"):
            raise Exception(f"Event update not successful: {response_data}")
        return response_data

    def delete_event(event_id):
        resp = requests.delete(f"{BASE_URL}/api/events/{event_id}", headers=HEADERS, timeout=TIMEOUT)
        if resp.status_code != 200:
            raise Exception(f"Event deletion failed: {resp.status_code} - {resp.text}")
        response_data = resp.json()
        if not response_data.get("success"):
            raise Exception(f"Event deletion not successful: {response_data}")
        return response_data

    # Prepare event data with intelligent date parsing (ISO 8601 format)
    now = datetime.datetime.utcnow()
    start1 = (now + datetime.timedelta(hours=1)).isoformat() + "Z"
    end1 = (now + datetime.timedelta(hours=2)).isoformat() + "Z"
    event1 = {
        "title": "Test Event 1",
        "description": "First test event",
        "start_date": start1,
        "end_date": end1,
        "location": "Conference Room A"
    }

    start2 = (now + datetime.timedelta(hours=1, minutes=30)).isoformat() + "Z"
    end2 = (now + datetime.timedelta(hours=2, minutes=30)).isoformat() + "Z"
    event2 = {
        "title": "Test Event 2",
        "description": "Second test event overlapping first",
        "start_date": start2,
        "end_date": end2,
        "location": "Conference Room B"
    }

    created_event1 = None
    created_event2 = None

    try:
        # Create first event
        created_response1 = create_event(event1)
        event1_id = created_response1.get("event_id")
        assert event1_id is not None, "Event creation response missing 'event_id'"
        created_event1 = created_response1.get("event", {})
        assert created_event1["title"] == event1["title"]

        # Create second event that may conflict with first event
        created_response2 = create_event(event2)
        event2_id = created_response2.get("event_id")
        assert event2_id is not None, "Event creation response missing 'event_id'"
        created_event2 = created_response2.get("event", {})
        assert created_event2["title"] == event2["title"]

        # Retrieve all events and verify both exist
        all_events = get_events()
        event1_found = any(event.get("id") == event1_id for event in all_events)
        event2_found = any(event.get("id") == event2_id for event in all_events)
        assert event1_found, f"Event 1 with ID {event1_id} not found in events list"
        assert event2_found, f"Event 2 with ID {event2_id} not found in events list"

        # Update event1's title and time to non-conflicting slot
        new_start = (now + datetime.timedelta(hours=3)).isoformat() + "Z"
        new_end = (now + datetime.timedelta(hours=4)).isoformat() + "Z"
        update_data = {
            "title": "Updated Test Event 1",
            "start_date": new_start,
            "end_date": new_end
        }
        updated_response = update_event(event1_id, update_data)
        updated_event1 = updated_response.get("event", {})
        assert updated_event1["title"] == update_data["title"]

        # Verify both events still exist after update
        all_events_after_update = get_events()
        event1_found_after = any(event.get("id") == event1_id for event in all_events_after_update)
        event2_found_after = any(event.get("id") == event2_id for event in all_events_after_update)
        assert event1_found_after, f"Event 1 with ID {event1_id} not found after update"
        assert event2_found_after, f"Event 2 with ID {event2_id} not found after update"

    finally:
        # Cleanup created events
        if event1_id:
            try:
                delete_event(event1_id)
            except Exception:
                pass
        if event2_id:
            try:
                delete_event(event2_id)
            except Exception:
                pass

test_calendar_event_management_and_conflict_detection()
