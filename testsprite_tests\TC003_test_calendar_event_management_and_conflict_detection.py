import requests
import datetime
import time

BASE_URL = "http://localhost:3000/api"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30

def test_calendar_event_management_and_conflict_detection():
    # Helper functions
    def create_event(event_data):
        resp = requests.post(f"{BASE_URL}/calendar/events", json=event_data, headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
        return resp.json()

    def get_event(event_id):
        resp = requests.get(f"{BASE_URL}/calendar/events/{event_id}", headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
        return resp.json()

    def update_event(event_id, update_data):
        resp = requests.put(f"{BASE_URL}/calendar/events/{event_id}", json=update_data, headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
        return resp.json()

    def delete_event(event_id):
        resp = requests.delete(f"{BASE_URL}/calendar/events/{event_id}", headers=HEADERS, timeout=TIMEOUT)
        if resp.status_code not in (200, 204):
            resp.raise_for_status()

    def list_events():
        resp = requests.get(f"{BASE_URL}/calendar/events", headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
        return resp.json()

    # Prepare event data with intelligent date parsing (ISO 8601 format)
    now = datetime.datetime.utcnow()
    start1 = (now + datetime.timedelta(hours=1)).isoformat() + "Z"
    end1 = (now + datetime.timedelta(hours=2)).isoformat() + "Z"
    event1 = {
        "title": "Test Event 1",
        "description": "First test event",
        "start_time": start1,
        "end_time": end1,
        "location": "Conference Room A"
    }

    start2 = (now + datetime.timedelta(hours=1, minutes=30)).isoformat() + "Z"
    end2 = (now + datetime.timedelta(hours=2, minutes=30)).isoformat() + "Z"
    event2 = {
        "title": "Test Event 2",
        "description": "Second test event overlapping first",
        "start_time": start2,
        "end_time": end2,
        "location": "Conference Room B"
    }

    created_event1 = None
    created_event2 = None

    try:
        # Create first event
        created_event1 = create_event(event1)
        assert "id" in created_event1, "Event creation response missing 'id'"
        assert created_event1["title"] == event1["title"]
        assert created_event1["start_time"] == event1["start_time"]
        assert created_event1["end_time"] == event1["end_time"]

        # Create second event that conflicts with first event
        created_event2 = create_event(event2)
        assert "id" in created_event2, "Event creation response missing 'id'"
        assert created_event2["title"] == event2["title"]

        # Check conflict detection response or flag
        # Assuming API returns a field "conflicts" listing conflicting event IDs
        conflicts = created_event2.get("conflicts", [])
        assert created_event1["id"] in conflicts, "Conflict detection failed: overlapping event not detected"

        # Retrieve event1 and verify details
        fetched_event1 = get_event(created_event1["id"])
        assert fetched_event1["id"] == created_event1["id"]
        assert fetched_event1["title"] == event1["title"]

        # Update event1's title and time to non-conflicting slot
        new_start = (now + datetime.timedelta(hours=3)).isoformat() + "Z"
        new_end = (now + datetime.timedelta(hours=4)).isoformat() + "Z"
        update_data = {
            "title": "Updated Test Event 1",
            "start_time": new_start,
            "end_time": new_end
        }
        updated_event1 = update_event(created_event1["id"], update_data)
        assert updated_event1["title"] == update_data["title"]
        assert updated_event1["start_time"] == update_data["start_time"]
        assert updated_event1["end_time"] == update_data["end_time"]

        # Verify conflict is resolved after update
        fetched_event2 = get_event(created_event2["id"])
        conflicts_after_update = fetched_event2.get("conflicts", [])
        assert created_event1["id"] not in conflicts_after_update, "Conflict not resolved after event update"

        # List all events and verify both exist
        events_list = list_events()
        event_ids = [e["id"] for e in events_list]
        assert created_event1["id"] in event_ids
        assert created_event2["id"] in event_ids

    finally:
        # Cleanup created events
        if created_event1:
            try:
                delete_event(created_event1["id"])
            except Exception:
                pass
        if created_event2:
            try:
                delete_event(created_event2["id"])
            except Exception:
                pass

test_calendar_event_management_and_conflict_detection()
