import requests
import datetime
import time

BASE_URL = "http://localhost:3000"
API_PREFIX = "/api/calendar/events"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30

def test_calendar_event_management_and_conflict_detection():
    created_event_ids = []

    def create_event(event_data):
        response = requests.post(f"{BASE_URL}{API_PREFIX}", json=event_data, headers=HEADERS, timeout=TIMEOUT)
        response.raise_for_status()
        event = response.json()
        created_event_ids.append(event["id"])
        return event

    def get_event(event_id):
        response = requests.get(f"{BASE_URL}{API_PREFIX}/{event_id}", headers=HEADERS, timeout=TIMEOUT)
        response.raise_for_status()
        return response.json()

    def update_event(event_id, update_data):
        response = requests.put(f"{BASE_URL}{API_PREFIX}/{event_id}", json=update_data, headers=HEADERS, timeout=TIMEOUT)
        response.raise_for_status()
        return response.json()

    def delete_event(event_id):
        response = requests.delete(f"{BASE_URL}{API_PREFIX}/{event_id}", headers=HEADERS, timeout=TIMEOUT)
        if response.status_code not in (200, 204):
            response.raise_for_status()

    try:
        # 1. Create an event with intelligent date parsing (natural language date)
        event_payload_1 = {
            "title": "Team Meeting",
            "description": "Discuss project updates",
            "start": "2025-08-01T10:00:00Z",
            "end": "2025-08-01T11:00:00Z"
        }
        event1 = create_event(event_payload_1)
        assert event1["title"] == event_payload_1["title"]
        assert event1["description"] == event_payload_1["description"]
        assert event1["start"] == event_payload_1["start"]
        assert event1["end"] == event_payload_1["end"]

        # 2. Create a second event that conflicts with the first event to test conflict detection
        event_payload_2 = {
            "title": "Client Call",
            "description": "Call with important client",
            "start": "2025-08-01T10:30:00Z",
            "end": "2025-08-01T11:30:00Z"
        }
        conflict_response = requests.post(f"{BASE_URL}{API_PREFIX}", json=event_payload_2, headers=HEADERS, timeout=TIMEOUT)
        if conflict_response.status_code == 409:
            # Conflict detected as expected
            conflict_data = conflict_response.json()
            assert "conflict" in conflict_data["detail"].lower() or "overlap" in conflict_data["detail"].lower()
        else:
            # If no conflict status, assume event created and add to cleanup
            conflict_response.raise_for_status()
            event2 = conflict_response.json()
            created_event_ids.append(event2["id"])
            # Verify overlap detection by querying conflicts endpoint if available
            conflicts_check = requests.get(f"{BASE_URL}{API_PREFIX}/{event2['id']}/conflicts", headers=HEADERS, timeout=TIMEOUT)
            if conflicts_check.status_code == 200:
                conflicts = conflicts_check.json()
                assert any(e["id"] == event1["id"] for e in conflicts)

        # 3. Retrieve the first event and verify details
        fetched_event1 = get_event(event1["id"])
        assert fetched_event1["id"] == event1["id"]
        assert fetched_event1["title"] == event1["title"]

        # 4. Update the first event's time to a non-conflicting slot
        updated_start = "2025-08-01T11:30:00Z"
        updated_end = "2025-08-01T12:30:00Z"
        update_payload = {
            "title": event1["title"],
            "description": event1["description"],
            "start": updated_start,
            "end": updated_end
        }
        updated_event = update_event(event1["id"], update_payload)
        assert updated_event["start"] == updated_start
        assert updated_event["end"] == updated_end

        # 5. Verify conflict no longer exists after update if second event was created
        if len(created_event_ids) > 1:
            conflicts_check = requests.get(f"{BASE_URL}{API_PREFIX}/{event1['id']}/conflicts", headers=HEADERS, timeout=TIMEOUT)
            if conflicts_check.status_code == 200:
                conflicts = conflicts_check.json()
                assert all(e["id"] != created_event_ids[1] for e in conflicts)

        # 6. Delete all created events and verify deletion
    finally:
        for eid in created_event_ids:
            try:
                delete_event(eid)
            except Exception:
                pass

test_calendar_event_management_and_conflict_detection()
