import { useState, useMemo, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CalendarEvent, 
  CalendarView, 
  CalendarState, 
  CalendarDay,
  CreateEventData 
} from './types'

/**
 * Calendar Component  
 * PATTERN: Interactive calendar with AI-generated events
 * Features:
 * - Month/Week/Day view modes with smooth transitions
 * - Drag and drop event management
 * - Real-time event creation and updates via API
 * - AI-generated event categorization
 * - Framer Motion animations for view transitions
 */
interface CalendarProps {
  // Props for controlled usage
  currentDate?: Date
  view?: CalendarView
  events?: CalendarEvent[]
  onDateSelect?: (date: Date) => void
  onEventSelect?: (event: CalendarEvent) => void
  onEventCreate?: (eventData: CreateEventData) => void
  onEventUpdate?: (eventId: string, updateData: Partial<CalendarEvent>) => void
  onEventDelete?: (eventId: string) => void
  onViewChange?: (view: CalendarView) => void
}

export function Calendar(props: CalendarProps = {}) {
  // Destructure props with defaults
  const {
    currentDate: propCurrentDate,
    events: propEvents,
    onDateSelect,
    onEventSelect,
    onEventCreate: _onEventCreate, // Unused but kept for future implementation
    onEventUpdate,
    onEventDelete,
    onViewChange
  } = props;

  // Use props if provided, otherwise use internal state with real API data
  const [calendarState, setCalendarState] = useState<CalendarState>({
    currentDate: propCurrentDate || new Date(),
    view: 'month',
    events: propEvents ? [] : [], // Load real events from API
    selectedEvent: undefined,
    isCreating: false,
    draggedEvent: undefined
  })
  
  const [loading, setLoading] = useState(!propEvents) // Only load if not using props
  const [error, setError] = useState<string | null>(null)

  // Load events from backend API when not using props
  useEffect(() => {
    if (propEvents) return // Skip if using props
    
    const loadEvents = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch('/api/v1/events', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data = await response.json()
        setCalendarState(prev => ({
          ...prev,
          events: data.events || []
        }))
      } catch (err) {
        console.error('Failed to load calendar events:', err)
        setError('Failed to load calendar events. Please try again.')
        // Fallback to empty array on error
        setCalendarState(prev => ({
          ...prev,
          events: []
        }))
      } finally {
        setLoading(false)
      }
    }
    
    loadEvents()
  }, [propEvents])

  // Generate calendar days for current month view
  const calendarDays = useMemo(() => {
    const year = calendarState.currentDate.getFullYear()
    const month = calendarState.currentDate.getMonth()
    
    // Get first day of month and how many days in month
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()
    
    const days: CalendarDay[] = []
    
    // Add empty days for previous month
    for (let i = 0; i < startingDayOfWeek; i++) {
      const prevDate = new Date(year, month, 0 - (startingDayOfWeek - 1 - i))
      days.push({
        date: prevDate,
        isCurrentMonth: false,
        isToday: false,
        events: []
      })
    }
    
    // Add days for current month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day)
      const isToday = date.toDateString() === new Date().toDateString()
      
      // Filter events for this day
      const dayEvents = calendarState.events.filter(event => {
        const eventDate = new Date(event.start_time)
        return eventDate.toDateString() === date.toDateString()
      })
      
      days.push({
        date,
        isCurrentMonth: true,
        isToday,
        events: dayEvents
      })
    }
    
    // Fill remaining slots for next month
    const remainingSlots = 42 - days.length // 6 rows × 7 days
    for (let i = 1; i <= remainingSlots; i++) {
      const nextDate = new Date(year, month + 1, i)
      days.push({
        date: nextDate,
        isCurrentMonth: false,
        isToday: false,
        events: []
      })
    }
    
    return days
  }, [calendarState.currentDate, calendarState.events])

  // Navigation handlers
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCalendarState(prev => {
      const newDate = new Date(prev.currentDate)
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1)
      } else {
        newDate.setMonth(newDate.getMonth() + 1)
      }
      return { ...prev, currentDate: newDate }
    })
  }

  const handleViewChange = (newView: CalendarView) => {
    setCalendarState(prev => ({ ...prev, view: newView }))
    if (onViewChange) {
      onViewChange(newView)
    }
  }

  // Event handlers
  const handleDayClick = (day: CalendarDay) => {
    if (onDateSelect) {
      onDateSelect(day.date)
    }
  }

  const handleEventClick = (event: CalendarEvent) => {
    setCalendarState(prev => ({ ...prev, selectedEvent: event }))
    if (onEventSelect) {
      onEventSelect(event)
    }
  }

  // Event update handler - for future implementation
  // @ts-ignore
  const handleEventUpdate = async (eventId: string, updateData: Partial<CalendarEvent>) => {
    try {
      const response = await fetch(`/api/v1/events/${eventId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })
      
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
      
      // Update local state
      setCalendarState(prev => ({
        ...prev,
        events: prev.events.map(event =>
          event.id === eventId ? { ...event, ...updateData } : event
        )
      }))
      
      if (onEventUpdate) {
        onEventUpdate(eventId, updateData)
      }
    } catch (err) {
      console.error('Failed to update event:', err)
      setError('Failed to update event. Please try again.')
    }
  }

  // Event deletion handler - for future implementation  
  // @ts-ignore
  const handleEventDelete = async (eventId: string) => {
    try {
      const response = await fetch(`/api/v1/events/${eventId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
      
      // Remove from local state
      setCalendarState(prev => ({
        ...prev,
        events: prev.events.filter(event => event.id !== eventId)
      }))
      
      if (onEventDelete) {
        onEventDelete(eventId)
      }
    } catch (err) {
      console.error('Failed to delete event:', err)
      setError('Failed to delete event. Please try again.')
    }
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.3,
        ease: "easeOut"
      }
    }
  }

  const dayVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { type: "spring", stiffness: 100, damping: 12 }
    },
    hover: { scale: 1.05, transition: { duration: 0.2 } }
  }

  const eventVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.2 }
    }
  }

  return (
    <motion.div 
      data-testid="calendar-container"
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">Loading calendar events...</span>
        </div>
      )}
      
      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-800">{error}</p>
          <button 
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 underline text-sm"
          >
            Dismiss
          </button>
        </div>
      )}
      
      {/* Calendar Header */}
      {!loading && (
      <div data-testid="calendar-header" className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <motion.h2 
            className="text-2xl font-semibold text-gray-900"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.3 }}
          >
            {calendarState.currentDate.toLocaleDateString('en-US', { 
              month: 'long', 
              year: 'numeric' 
            })}
          </motion.h2>
          
          {/* Navigation Buttons */}
          <div className="flex gap-2">
            <motion.button
              onClick={() => navigateMonth('prev')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              ←
            </motion.button>
            <motion.button
              onClick={() => navigateMonth('next')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              →
            </motion.button>
          </div>
        </div>
        
        {/* View Switcher */}
        <div className="flex gap-2">
          {(['month', 'week', 'day'] as CalendarView[]).map(view => (
            <motion.button
              key={view}
              onClick={() => handleViewChange(view)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                calendarState.view === view
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {view.charAt(0).toUpperCase() + view.slice(1)}
            </motion.button>
          ))}
        </div>
      </div>
      )}

      {/* Calendar Grid */}
      {!loading && (
      <div data-testid="calendar-grid">
        <AnimatePresence mode="wait">
          {calendarState.view === 'month' && (
            <motion.div
              key="month-view"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-7 gap-1"
              variants={containerVariants}
            >
              {/* Day headers */}
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                  {day}
                </div>
              ))}
              
              {/* Calendar days */}
              {calendarDays.map((day, index) => (
                <motion.div
                  key={`${day.date.getTime()}-${index}`}
                  variants={dayVariants}
                  whileHover="hover"
                  className={`
                    p-2 min-h-[80px] border border-gray-100 cursor-pointer
                    ${day.isCurrentMonth ? 'bg-white' : 'bg-gray-50 text-gray-400'}
                    ${day.isToday ? 'bg-blue-50 border-blue-200' : ''}
                    hover:bg-gray-50 transition-colors
                  `}
                  onClick={() => handleDayClick(day)}
                >
                  <div className="text-sm font-medium mb-1">
                    {day.date.getDate()}
                  </div>
                  
                  {/* Events for this day */}
                  <div className="space-y-1">
                    {day.events.slice(0, 2).map((event) => (
                      <motion.div
                        key={event.id}
                        variants={eventVariants}
                        className={`
                          text-xs p-1 rounded truncate cursor-pointer
                          bg-blue-100 text-blue-800 hover:bg-blue-200
                        `}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEventClick(event)
                        }}
                      >
                        {event.title}
                      </motion.div>
                    ))}
                    
                    {/* Show more indicator */}
                    {day.events.length > 2 && (
                      <div className="text-xs text-gray-500">
                        +{day.events.length - 2} more
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}
          
          {/* Week View Placeholder */}
          {calendarState.view === 'week' && (
            <motion.div
              key="week-view"
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              className="text-center py-20 text-gray-500"
            >
              Week view coming soon...
            </motion.div>
          )}
          
          {/* Day View Placeholder */}
          {calendarState.view === 'day' && (
            <motion.div
              key="day-view"
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              className="text-center py-20 text-gray-500"
            >
              Day view coming soon...
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      )}

      {/* Quick Stats */}
      {!loading && (
      <motion.div 
        className="mt-6 pt-4 border-t border-gray-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <div className="flex justify-between text-sm text-gray-600">
          <span>Total Events: {calendarState.events.length}</span>
          <span>AI Generated: {calendarState.events.filter(e => e.ai_generated).length}</span>
        </div>
      </motion.div>
      )}
    </motion.div>
  )
}
