"""
Calendar operations tool for AI-Powered Dashboard.

This tool provides CRUD operations for calendar events using Mirascope tool patterns
following research/mirascope/page6-tools.md for tool implementation.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field

from mirascope.core.base.tool import BaseTool
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from app.database.connection import get_db_session, DatabaseTransaction
from app.database.models import Event as EventModel, ProcessingLog
from app.models.pydantic_models import EventData, EventStatus
from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CreateEventInput(BaseModel):
    """Input model for creating calendar events."""
    title: str = Field(..., description="Event title")
    description: Optional[str] = Field(None, description="Event description")
    location: Optional[str] = Field(None, description="Event location")
    start_datetime: datetime = Field(..., description="Event start date and time")
    end_datetime: Optional[datetime] = Field(None, description="Event end date and time")
    all_day: bool = Field(default=False, description="Whether this is an all-day event")
    event_type: Optional[str] = Field(None, description="Type of event")
    reminder_minutes: Optional[int] = Field(15, description="Reminder time in minutes before event")


class UpdateEventInput(BaseModel):
    """Input model for updating calendar events."""
    event_id: int = Field(..., description="ID of event to update")
    title: Optional[str] = Field(None, description="New event title")
    description: Optional[str] = Field(None, description="New event description")
    location: Optional[str] = Field(None, description="New event location")
    start_datetime: Optional[datetime] = Field(None, description="New start date and time")
    end_datetime: Optional[datetime] = Field(None, description="New end date and time")
    status: Optional[EventStatus] = Field(None, description="New event status")


class SearchEventsInput(BaseModel):
    """Input model for searching calendar events."""
    start_date: Optional[datetime] = Field(None, description="Search from this date")
    end_date: Optional[datetime] = Field(None, description="Search until this date")
    event_type: Optional[str] = Field(None, description="Filter by event type")
    status: Optional[EventStatus] = Field(None, description="Filter by event status")
    search_text: Optional[str] = Field(None, description="Search in title/description")
    limit: int = Field(50, description="Maximum number of results")


class CalendarTool(BaseTool):
    """
    Mirascope tool for calendar operations.
    
    This tool provides comprehensive calendar management functionality
    with proper database integration and conflict detection.
    """
    
    def __init__(self):
        super().__init__()
    
    async def create_event(self, input_data: CreateEventInput) -> Dict[str, Any]:
        """
        Create a new calendar event.
        
        Args:
            input_data: Event creation parameters
            
        Returns:
            Dict[str, Any]: Created event data with conflict information
        """
        try:
            logger.info(f"Creating event: {input_data.title}")
            
            # Check for conflicts before creating
            conflicts = await self._check_conflicts(
                input_data.start_datetime,
                input_data.end_datetime or (input_data.start_datetime + timedelta(hours=1))
            )
            
            async with DatabaseTransaction() as session:
                # Create new event model
                event_model = EventModel(
                    title=input_data.title,
                    description=input_data.description,
                    location=input_data.location,
                    start_datetime=input_data.start_datetime,
                    end_datetime=input_data.end_datetime,
                    all_day=input_data.all_day,
                    event_type=input_data.event_type or "general",
                    reminder_minutes=input_data.reminder_minutes,
                    status=EventStatus.SCHEDULED.value,
                    ai_conflicts=[c["message"] for c in conflicts] if conflicts else None
                )
                
                session.add(event_model)
                await session.flush()  # Get ID without committing
                
                # Convert to EventData for response
                event_data = EventData(
                    id=str(event_model.id),
                    title=event_model.title,
                    description=event_model.description,
                    location=event_model.location,
                    start_datetime=event_model.start_datetime,
                    end_datetime=event_model.end_datetime,
                    all_day=event_model.all_day,
                    event_type=event_model.event_type,
                    reminder_minutes=event_model.reminder_minutes,
                    status=EventStatus(event_model.status),
                    created_at=event_model.created_at,
                    ai_conflicts=[c["message"] for c in conflicts] if conflicts else None
                )
                
                logger.info(f"Event created successfully: ID {event_model.id}")
                
                return {
                    "success": True,
                    "event": event_data.dict(),
                    "conflicts": conflicts,
                    "message": f"Event '{input_data.title}' created successfully" + 
                              (f" with {len(conflicts)} conflicts detected" if conflicts else "")
                }
                
        except Exception as e:
            logger.error(f"Error creating event: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create event"
            }
    
    async def update_event(self, input_data: UpdateEventInput) -> Dict[str, Any]:
        """
        Update an existing calendar event.
        
        Args:
            input_data: Event update parameters
            
        Returns:
            Dict[str, Any]: Updated event data
        """
        try:
            logger.info(f"Updating event: {input_data.event_id}")
            
            async with DatabaseTransaction() as session:
                # Find existing event
                result = await session.execute(
                    select(EventModel).where(EventModel.id == input_data.event_id)
                )
                event_model = result.scalar_one_or_none()
                
                if not event_model:
                    return {
                        "success": False,
                        "error": "Event not found",
                        "message": f"Event with ID {input_data.event_id} not found"
                    }
                
                # Update fields that are provided
                if input_data.title is not None:
                    event_model.title = input_data.title
                if input_data.description is not None:
                    event_model.description = input_data.description
                if input_data.location is not None:
                    event_model.location = input_data.location
                if input_data.start_datetime is not None:
                    event_model.start_datetime = input_data.start_datetime
                if input_data.end_datetime is not None:
                    event_model.end_datetime = input_data.end_datetime
                if input_data.status is not None:
                    event_model.status = input_data.status.value
                
                event_model.updated_at = datetime.now()
                
                # Check for new conflicts if time changed
                conflicts = []
                if input_data.start_datetime or input_data.end_datetime:
                    conflicts = await self._check_conflicts(
                        event_model.start_datetime,
                        event_model.end_datetime or (event_model.start_datetime + timedelta(hours=1)),
                        exclude_event_id=input_data.event_id
                    )
                    event_model.ai_conflicts = [c["message"] for c in conflicts] if conflicts else None
                
                # Convert to EventData for response
                event_data = EventData(
                    id=str(event_model.id),
                    title=event_model.title,
                    description=event_model.description,
                    location=event_model.location,
                    start_datetime=event_model.start_datetime,
                    end_datetime=event_model.end_datetime,
                    all_day=event_model.all_day,
                    event_type=event_model.event_type,
                    reminder_minutes=event_model.reminder_minutes,
                    status=EventStatus(event_model.status),
                    created_at=event_model.created_at,
                    updated_at=event_model.updated_at,
                    ai_conflicts=[c["message"] for c in conflicts] if conflicts else None
                )
                
                logger.info(f"Event updated successfully: ID {input_data.event_id}")
                
                return {
                    "success": True,
                    "event": event_data.dict(),
                    "conflicts": conflicts,
                    "message": f"Event updated successfully"
                }
                
        except Exception as e:
            logger.error(f"Error updating event: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to update event"
            }
    
    async def delete_event(self, event_id: int) -> Dict[str, Any]:
        """
        Delete a calendar event.
        
        Args:
            event_id: ID of event to delete
            
        Returns:
            Dict[str, Any]: Deletion result
        """
        try:
            logger.info(f"Deleting event: {event_id}")
            
            async with DatabaseTransaction() as session:
                # Find and delete event
                result = await session.execute(
                    select(EventModel).where(EventModel.id == event_id)
                )
                event_model = result.scalar_one_or_none()
                
                if not event_model:
                    return {
                        "success": False,
                        "error": "Event not found",
                        "message": f"Event with ID {event_id} not found"
                    }
                
                event_title = event_model.title
                await session.delete(event_model)
                
                logger.info(f"Event deleted successfully: ID {event_id}")
                
                return {
                    "success": True,
                    "message": f"Event '{event_title}' deleted successfully"
                }
                
        except Exception as e:
            logger.error(f"Error deleting event: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to delete event"
            }
    
    async def search_events(self, input_data: SearchEventsInput) -> Dict[str, Any]:
        """
        Search for calendar events.
        
        Args:
            input_data: Search parameters
            
        Returns:
            Dict[str, Any]: Search results
        """
        try:
            logger.info(f"Searching events with filters: {input_data.dict()}")
            
            async with DatabaseTransaction() as session:
                # Build query
                query = select(EventModel)
                conditions = []
                
                # Date range filters
                if input_data.start_date:
                    conditions.append(EventModel.start_datetime >= input_data.start_date)
                if input_data.end_date:
                    conditions.append(EventModel.start_datetime <= input_data.end_date)
                
                # Type and status filters
                if input_data.event_type:
                    conditions.append(EventModel.event_type == input_data.event_type)
                if input_data.status:
                    conditions.append(EventModel.status == input_data.status.value)
                
                # Text search
                if input_data.search_text:
                    search_term = f"%{input_data.search_text}%"
                    conditions.append(
                        or_(
                            EventModel.title.ilike(search_term),
                            EventModel.description.ilike(search_term),
                            EventModel.location.ilike(search_term)
                        )
                    )
                
                # Apply conditions
                if conditions:
                    query = query.where(and_(*conditions))
                
                # Order by start time
                query = query.order_by(EventModel.start_datetime).limit(input_data.limit)
                
                # Execute query
                result = await session.execute(query)
                event_models = result.scalars().all()
                
                # Convert to EventData objects
                events = []
                for event_model in event_models:
                    event_data = EventData(
                        id=str(event_model.id),
                        title=event_model.title,
                        description=event_model.description,
                        location=event_model.location,
                        start_datetime=event_model.start_datetime,
                        end_datetime=event_model.end_datetime,
                        all_day=event_model.all_day,
                        event_type=event_model.event_type,
                        reminder_minutes=event_model.reminder_minutes,
                        status=EventStatus(event_model.status),
                        created_at=event_model.created_at,
                        updated_at=event_model.updated_at,
                        ai_conflicts=event_model.ai_conflicts
                    )
                    events.append(event_data.dict())
                
                logger.info(f"Found {len(events)} events matching search criteria")
                
                return {
                    "success": True,
                    "events": events,
                    "count": len(events),
                    "message": f"Found {len(events)} events"
                }
                
        except Exception as e:
            logger.error(f"Error searching events: {e}")
            return {
                "success": False,
                "error": str(e),
                "events": [],
                "count": 0,
                "message": "Failed to search events"
            }
    
    async def get_upcoming_events(self, days_ahead: int = 7) -> Dict[str, Any]:
        """
        Get upcoming events for the next N days.
        
        Args:
            days_ahead: Number of days to look ahead
            
        Returns:
            Dict[str, Any]: Upcoming events
        """
        try:
            now = datetime.now()
            end_date = now + timedelta(days=days_ahead)
            
            search_input = SearchEventsInput(
                start_date=now,
                end_date=end_date,
                status=EventStatus.SCHEDULED,
                limit=20
            )
            
            result = await self.search_events(search_input)
            
            if result["success"]:
                result["message"] = f"Found {result['count']} upcoming events in next {days_ahead} days"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting upcoming events: {e}")
            return {
                "success": False,
                "error": str(e),
                "events": [],
                "count": 0,
                "message": "Failed to get upcoming events"
            }
    
    async def _check_conflicts(
        self, 
        start_time: datetime, 
        end_time: datetime,
        exclude_event_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Check for scheduling conflicts with existing events.
        
        Args:
            start_time: Proposed start time
            end_time: Proposed end time
            exclude_event_id: Event ID to exclude from conflict check
            
        Returns:
            List[Dict[str, Any]]: List of conflicts found
        """
        try:
            async with DatabaseTransaction() as session:
                # Find overlapping events
                query = select(EventModel).where(
                    and_(
                        EventModel.status == EventStatus.SCHEDULED.value,
                        EventModel.start_datetime < end_time,
                        or_(
                            EventModel.end_datetime.is_(None),
                            EventModel.end_datetime > start_time
                        )
                    )
                )
                
                # Exclude specific event if provided
                if exclude_event_id:
                    query = query.where(EventModel.id != exclude_event_id)
                
                result = await session.execute(query)
                conflicting_events = result.scalars().all()
                
                conflicts = []
                for event in conflicting_events:
                    conflicts.append({
                        "event_id": event.id,
                        "event_title": event.title,
                        "event_start": event.start_datetime,
                        "event_end": event.end_datetime,
                        "message": f"Conflicts with '{event.title}' at {event.start_datetime.strftime('%I:%M %p')}"
                    })
                
                return conflicts
                
        except Exception as e:
            logger.error(f"Error checking conflicts: {e}")
            return []
    
    async def call(self, operation: str, **kwargs) -> Dict[str, Any]:
        """
        Main tool call method for Mirascope integration.
        
        Args:
            operation: Operation to perform (create, update, delete, search, upcoming)
            **kwargs: Operation-specific parameters
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if operation == "create":
                input_data = CreateEventInput(**kwargs)
                return await self.create_event(input_data)
            
            elif operation == "update":
                input_data = UpdateEventInput(**kwargs)
                return await self.update_event(input_data)
            
            elif operation == "delete":
                event_id = kwargs.get("event_id")
                if not event_id:
                    return {"success": False, "error": "event_id required for delete operation"}
                return await self.delete_event(event_id)
            
            elif operation == "search":
                input_data = SearchEventsInput(**kwargs)
                return await self.search_events(input_data)
            
            elif operation == "upcoming":
                days_ahead = kwargs.get("days_ahead", 7)
                return await self.get_upcoming_events(days_ahead)
            
            else:
                return {
                    "success": False,
                    "error": f"Unknown operation: {operation}",
                    "available_operations": ["create", "update", "delete", "search", "upcoming"]
                }
                
        except Exception as e:
            logger.error(f"Error in calendar tool call: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Calendar tool error: {str(e)}"
            }


# Global calendar tool instance
_calendar_tool = None


def get_calendar_tool() -> CalendarTool:
    """Get the global calendar tool instance (singleton pattern)."""
    global _calendar_tool
    if _calendar_tool is None:
        _calendar_tool = CalendarTool()
    return _calendar_tool


# Export main components
__all__ = [
    "CalendarTool",
    "CreateEventInput",
    "UpdateEventInput", 
    "SearchEventsInput",
    "get_calendar_tool"
]
