/**
 * API Configuration and Endpoints
 * Centralized API configuration to ensure consistency across the frontend
 */

// Base API configuration
export const API_CONFIG = {
  BASE_URL: '/api/v1', // Updated to match backend API versioning
  TIMEOUT: 30000, // 30 second timeout
} as const

// API Endpoints
export const API_ENDPOINTS = {
  // Settings endpoints
  SETTINGS: `${API_CONFIG.BASE_URL}/settings`,
  
  // AI processing endpoints
  PROCESS_INPUT: `${API_CONFIG.BASE_URL}/process`,
  
  // Task management endpoints
  TASKS: `${API_CONFIG.BASE_URL}/tasks`,
  TASK_BY_ID: (id: string) => `${API_CONFIG.BASE_URL}/tasks/${id}`,
  
  // Calendar/Event endpoints
  EVENTS: `${API_CONFIG.BASE_URL}/events`,
  EVENT_BY_ID: (id: string) => `${API_CONFIG.BASE_URL}/events/${id}`,
  
  // Search endpoints
  SEARCH: `${API_CONFIG.BASE_URL}/search`,
  WEB_SEARCH: `${API_CONFIG.BASE_URL}/search/web`,
  
  // Health check
  HEALTH: `${API_CONFIG.BASE_URL}/health`,
} as const

// WebSocket configuration
export const WS_CONFIG = {
  URL: '/ws', // Use relative URL to go through Vite proxy
  RECONNECT_DELAY: 3000,
  MAX_RECONNECT_ATTEMPTS: 5,
} as const

// HTTP client configuration
export const createApiClient = () => {
  const baseHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }

  return {
    get: async (url: string, options?: RequestInit) => {
      const response = await fetch(url, {
        method: 'GET',
        headers: baseHeaders,
        ...options,
      })
      return response
    },
    
    post: async (url: string, data?: any, options?: RequestInit) => {
      const response = await fetch(url, {
        method: 'POST',
        headers: baseHeaders,
        body: data ? JSON.stringify(data) : undefined,
        ...options,
      })
      return response
    },
    
    put: async (url: string, data?: any, options?: RequestInit) => {
      const response = await fetch(url, {
        method: 'PUT',
        headers: baseHeaders,
        body: data ? JSON.stringify(data) : undefined,
        ...options,
      })
      return response
    },
    
    delete: async (url: string, options?: RequestInit) => {
      const response = await fetch(url, {
        method: 'DELETE',
        headers: baseHeaders,
        ...options,
      })
      return response
    },
  }
}

export const apiClient = createApiClient()
