#!/usr/bin/env python3
"""
Test exact frontend message format to identify the WebSocket processing issue.
"""

import asyncio
import websockets
import json

async def test_frontend_format():
    """Test the exact message format that the frontend sends."""
    try:
        uri = 'ws://localhost:8000/ws'
        async with websockets.connect(uri) as websocket:
            print('✅ WebSocket connected successfully!')
            
            # Send connection message first
            connect_message = {
                'type': 'connect',
                'payload': {
                    'clientId': 'test-frontend-format'
                }
            }
            await websocket.send(json.dumps(connect_message))
            
            # Read connection response
            response = await websocket.recv()
            print('✅ Connection confirmed')
            
            # Test with exact frontend format
            frontend_message = {
                'type': 'process_input',
                'payload': {
                    'input': 'Create a task to test frontend format'
                }
            }
            print(f'📤 Sending frontend format: {json.dumps(frontend_message, indent=2)}')
            await websocket.send(json.dumps(frontend_message))
            
            # Wait for responses
            for i in range(5):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(response)
                    msg_type = data.get('type')
                    payload = data.get('payload', {})
                    message = payload.get('message', 'No message')
                    print(f'📥 Response {i+1}: {msg_type} - {message}')
                except asyncio.TimeoutError:
                    print(f'⏰ Timeout waiting for response {i+1}')
                    break
                    
    except Exception as e:
        print(f'❌ Test failed: {e}')

if __name__ == '__main__':
    asyncio.run(test_frontend_format())
