{"tech_stack": ["Python", "FastAPI", "TypeScript", "React", "Vite", "SQLite", "Mirascope", "Framer Motion", "TailwindCSS", "WebSockets", "Ollama", "OpenAI API", "Pydantic", "SQLAlchemy", "Zustand", "React Hook Form", "A<PERSON>os"], "features": [{"name": "AI Orchestrator", "description": "Central AI orchestration system using Mirascope that processes user input and routes to appropriate agents", "files": ["backend/app/agents/orchestrator.py", "backend/app/main.py"]}, {"name": "Task Management Agent", "description": "AI agent that processes task-related inputs and manages task operations", "files": ["backend/app/agents/task_agent.py", "backend/app/tools/task_tool.py"]}, {"name": "Calendar Agent", "description": "AI agent that handles calendar events and scheduling operations", "files": ["backend/app/agents/calendar_agent.py", "backend/app/tools/calendar_tool.py"]}, {"name": "Search Agent", "description": "AI agent that processes search queries and routes to web or database search", "files": ["backend/app/agents/search_agent.py", "backend/app/tools/web_search_tool.py", "backend/app/tools/database_search_tool.py"]}, {"name": "Dashboard UI", "description": "Main dashboard interface with hero input and visual feedback animations", "files": ["frontend/src/pages/Dashboard.tsx", "frontend/src/components/Dashboard.tsx", "frontend/src/components/dashboard"]}, {"name": "Task Management UI", "description": "Task list interface with CRUD operations and animations", "files": ["frontend/src/pages/Tasks.tsx", "frontend/src/components/tasks"]}, {"name": "Calendar UI", "description": "Calendar interface for viewing and managing events", "files": ["frontend/src/pages/Events.tsx", "frontend/src/components/calendar"]}, {"name": "WebSocket Communication", "description": "Real-time communication between frontend and backend for AI processing updates", "files": ["backend/app/api/websockets.py", "frontend/src/hooks/useWebSocket.ts", "backend/app/services/websocket_manager.py"]}, {"name": "Database Layer", "description": "SQLite database with SQLAlchemy ORM for data persistence", "files": ["backend/app/database/connection.py", "backend/app/database/models.py", "backend/app/database/migrations.py", "backend/app/database/base.py"]}, {"name": "Embedding Service", "description": "Semantic search using Ollama embeddings for intelligent data retrieval", "files": ["backend/app/services/embedding_service.py", "backend/app/tools/embedding_tool.py"]}, {"name": "API Routes", "description": "RESTful API endpoints for all application operations", "files": ["backend/app/api/routes.py", "backend/app/api/security.py"]}, {"name": "Animation System", "description": "Framer Motion based animation system for smooth UI transitions", "files": ["frontend/src/hooks/useAnimations.ts", "frontend/src/components/ui"]}, {"name": "State Management", "description": "Zustand-based global state management for application data", "files": ["frontend/src/stores/appStore.ts"]}, {"name": "AI Service Integration", "description": "Frontend service layer for AI orchestrator communication", "files": ["frontend/src/services/aiService.ts", "frontend/src/hooks/useAIOrchestrator.ts"]}, {"name": "Settings Management", "description": "Application settings and configuration management", "files": ["frontend/src/pages/Settings.tsx", "frontend/src/components/settings", "backend/app/config/settings.py"]}, {"name": "Erro<PERSON>", "description": "Comprehensive error handling and recovery system", "files": ["frontend/src/components/ErrorBoundary.tsx", "frontend/src/utils/errorHandling.ts", "backend/app/utils/error_handling.py", "backend/app/middleware/error_middleware.py"]}, {"name": "Performance Monitoring", "description": "Performance tracking and optimization features", "files": ["frontend/src/utils/performance.ts", "backend/app/middleware/performance.py"]}, {"name": "Security Middleware", "description": "Security features including rate limiting and request validation", "files": ["backend/app/middleware/security_middleware.py", "backend/app/middleware/rate_limit.py", "backend/app/config/security.py"]}]}