import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Calendar, Plus, Search, Clock, MapPin } from 'lucide-react'
import { API_ENDPOINTS } from '../config/api'

interface Event {
  id: string
  title: string
  description?: string
  location?: string
  start_datetime: string
  end_datetime?: string
  all_day: boolean
  event_type?: string
  status: 'scheduled' | 'completed' | 'cancelled'
  created_at: string
  updated_at?: string
  ai_confidence?: number
}

interface EventResponse {
  success: boolean
  events: Event[]
  message?: string
  error?: string
}

/**
 * Events page component - AI-powered calendar management with data fetching
 */
const Events: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'today' | 'upcoming'>('all')

  // Fetch events on component mount
  useEffect(() => {
    console.log('🚀 Events component mounted - useEffect running!')
    const fetchEvents = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(API_ENDPOINTS.EVENTS)
        const data: EventResponse = await response.json()
        
        if (data.success && data.events) {
          setEvents(data.events)
          console.log(`✅ Successfully loaded ${data.events.length} events`, data.events)
        } else {
          console.error('❌ API returned error:', data.error || data.message)
          setError(data.error || data.message || 'Failed to load events')
        }
      } catch (err) {
        console.error('❌ Network error fetching events:', err)
        setError('Network error - please check your connection')
      } finally {
        setIsLoading(false)
      }
    }

    fetchEvents()
  }, [])

  // Filter events based on search and filter criteria
  const filteredEvents = events.filter(event => {
    const matchesSearch = !searchTerm || 
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.toLowerCase().includes(searchTerm.toLowerCase())

    const now = new Date()
    const eventDate = new Date(event.start_datetime)
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const eventDay = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate())

    let matchesFilter = true
    if (selectedFilter === 'today') {
      matchesFilter = eventDay.getTime() === today.getTime()
    } else if (selectedFilter === 'upcoming') {
      matchesFilter = eventDate > now
    }

    return matchesSearch && matchesFilter
  })

  // Get event status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'text-accent-blue bg-accent-blue/10'
      case 'completed': return 'text-accent-green bg-accent-green/10'
      case 'cancelled': return 'text-accent-red bg-accent-red/10'
      default: return 'text-text-secondary bg-background-secondary'
    }
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Loading state
  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="p-6 min-h-screen"
      >
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-blue"></div>
          <span className="ml-4 text-text-secondary">Loading events...</span>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 min-h-screen"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Events</h1>
          <p className="text-text-secondary">AI-powered calendar and event management</p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="button-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          Add Event
        </motion.button>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-accent-red/10 border border-accent-red/20 rounded-xl p-4 mb-6"
        >
          <p className="text-accent-red font-medium">Error: {error}</p>
        </motion.div>
      )}

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-8">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-secondary" />
          <input
            type="text"
            placeholder="Search events..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-background-secondary border border-border-primary rounded-xl text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-blue transition-all"
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2">
          {[
            { key: 'all', label: 'All Events' },
            { key: 'today', label: 'Today' },
            { key: 'upcoming', label: 'Upcoming' }
          ].map((filter) => (
            <motion.button
              key={filter.key}
              whileHover={{ scale: 1.02 }}
              onClick={() => setSelectedFilter(filter.key as any)}
              className={`px-4 py-2 rounded-xl transition-colors ${
                selectedFilter === filter.key
                  ? 'bg-accent-blue text-white'
                  : 'bg-background-secondary text-text-secondary hover:text-text-primary border border-border-primary'
              }`}
            >
              {filter.label}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Events List */}
      <div className="space-y-4">
        {filteredEvents.length === 0 ? (
          /* Empty State */
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            <div className="w-20 h-20 bg-accent-green/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Calendar className="w-10 h-10 text-accent-green" />
            </div>
            
            <h2 className="text-2xl font-semibold text-text-primary mb-4">
              {searchTerm ? 'No Matching Events' : 'No Events Found'}
            </h2>
            
            <p className="text-text-secondary mb-6 max-w-md mx-auto">
              {searchTerm 
                ? 'Try adjusting your search terms or filter criteria.'
                : 'Ask the AI to schedule meetings, set reminders, or create calendar events from the main dashboard.'
              }
            </p>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.location.href = '/dashboard'}
              className="button-primary"
            >
              Go to Dashboard
            </motion.button>
          </motion.div>
        ) : (
          /* Events List */
          filteredEvents.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-background-secondary border border-border-primary rounded-xl p-6 hover:border-accent-blue/50 transition-all cursor-pointer"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-text-primary">
                      {event.title}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                      {event.status}
                    </span>
                  </div>
                  
                  {event.description && (
                    <p className="text-text-secondary mb-3">
                      {event.description}
                    </p>
                  )}
                  
                  <div className="flex flex-wrap items-center gap-4 text-sm text-text-secondary">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatDate(event.start_datetime)}</span>
                    </div>
                    
                    {event.location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span>{event.location}</span>
                      </div>
                    )}
                    
                    {event.event_type && (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span className="capitalize">{event.event_type}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {event.ai_confidence && (
                  <div className="text-xs text-text-secondary bg-background-tertiary px-2 py-1 rounded-lg ml-4">
                    AI: {Math.round(event.ai_confidence * 100)}%
                  </div>
                )}
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* Summary */}
      <div className="mt-8 pt-6 border-t border-border-primary">
        <p className="text-text-secondary text-center">
          Showing {filteredEvents.length} of {events.length} events
        </p>
      </div>
    </motion.div>
  )
}

export default Events
