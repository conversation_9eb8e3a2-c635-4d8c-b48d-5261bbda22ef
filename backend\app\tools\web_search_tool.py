"""
Web search tool for AI-Powered Dashboard with LangSearch integration.

This tool provides web search capabilities following the LangSearch API documentation
from research/langsearch/page1-web-search-api.md with rate limiting and error handling.
"""

import asyncio
import logging
import json
import aiohttp
from typing import List, Dict, Any, Optional, Literal
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator

# Import settings safely - handle missing config
logger = logging.getLogger(__name__)

try:
    from app.config.settings import get_settings
    settings = get_settings()
except Exception as e:
    logger.warning(f"Settings not available: {e}")
    settings = None


class WebSearchInput(BaseModel):
    """Input model for web search operations."""
    query: str = Field(..., description="Search query text")
    freshness: Literal["oneDay", "oneWeek", "oneMonth", "oneYear", "noLimit"] = Field(
        default="noLimit", 
        description="Time range for search results"
    )
    summary: bool = Field(default=True, description="Include long text summaries for results")
    count: int = Field(default=10, description="Number of results to return (1-10)")
    include_metadata: bool = Field(default=True, description="Include search metadata in response")
    
    @validator('count')
    def validate_count(cls, v):
        if not 1 <= v <= 10:
            raise ValueError("Count must be between 1 and 10")
        return v


class WebSearchTool:
    """
    Web search tool using LangSearch API for comprehensive web search capabilities.
    
    Features:
    - Multi-site search with source attribution
    - Result ranking and filtering  
    - Rate limiting and error handling
    - Structured response formatting
    - Time-based freshness filtering
    """
    
    # Class-level configuration
    LANGSEARCH_ENDPOINT = "https://api.langsearch.com/v1/web-search"
    RATE_LIMIT_REQUESTS = 100  # requests per window
    RATE_LIMIT_WINDOW = 3600   # 1 hour in seconds
    
    def __init__(self):
        super().__init__()
        # Use a simple list for rate limiting that doesn't need to be a model field
        object.__setattr__(self, 'request_timestamps', [])
    
    @property
    def api_key(self):
        """Get API key from settings or environment."""
        if settings:
            api_key = getattr(settings, 'langsearch_api_key', None)
            if api_key:
                return api_key
        
        # Fallback to environment variable
        import os
        return os.getenv('LANGSEARCH_API_KEY')
    
    @property 
    def langsearch_endpoint(self):
        """Get LangSearch endpoint URL."""
        return self.LANGSEARCH_ENDPOINT
    
    async def web_search(self, input_data: WebSearchInput) -> Dict[str, Any]:
        """
        Perform web search using LangSearch API.
        
        Args:
            input_data: Web search parameters
            
        Returns:
            Dict[str, Any]: Search results with metadata
        """
        try:
            logger.info(f"Performing web search: '{input_data.query}' (count: {input_data.count})")
            
            # Check API key availability
            if not self.api_key:
                return {
                    "success": False,
                    "error": "LangSearch API key not configured",
                    "message": "Web search requires LangSearch API key in settings",
                    "fallback_suggestion": "Configure LANGSEARCH_API_KEY environment variable"
                }
            
            # Check rate limits
            rate_limit_check = await self._check_rate_limits()
            if not rate_limit_check["allowed"]:
                return {
                    "success": False,
                    "error": "Rate limit exceeded",
                    "message": rate_limit_check["message"],
                    "retry_after": rate_limit_check.get("retry_after")
                }
            
            # Prepare search request
            search_payload = {
                "query": input_data.query,
                "freshness": input_data.freshness,
                "summary": input_data.summary,
                "count": input_data.count
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Execute search request
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.post(
                    self.langsearch_endpoint,
                    json=search_payload,
                    headers=headers
                ) as response:
                    
                    # Record request timestamp for rate limiting
                    self.request_timestamps.append(datetime.now())
                    
                    if response.status == 200:
                        response_data = await response.json()
                        
                        # Process and format results
                        formatted_results = await self._process_search_results(
                            response_data,
                            input_data.query,
                            input_data.include_metadata
                        )
                        
                        logger.info(f"Web search completed: {len(formatted_results)} results")
                        
                        return {
                            "success": True,
                            "query": input_data.query,
                            "results": formatted_results,
                            "count": len(formatted_results),
                            "search_metadata": {
                                "freshness": input_data.freshness,
                                "summary_included": input_data.summary,
                                "api_endpoint": "LangSearch",
                                "search_timestamp": datetime.now().isoformat(),
                                "rate_limit_remaining": await self._get_rate_limit_remaining()
                            },
                            "message": f"Found {len(formatted_results)} web results"
                        }
                    
                    elif response.status == 429:  # Rate limited
                        return {
                            "success": False,
                            "error": "API rate limit exceeded", 
                            "message": "LangSearch API rate limit exceeded - try again later",
                            "status_code": response.status
                        }
                    
                    elif response.status == 401:  # Unauthorized
                        return {
                            "success": False,
                            "error": "Invalid API key",
                            "message": "LangSearch API key is invalid or expired",
                            "status_code": response.status
                        }
                    
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"API request failed",
                            "message": f"LangSearch API returned status {response.status}",
                            "status_code": response.status,
                            "error_details": error_text[:500]  # Truncate long errors
                        }
                        
        except aiohttp.ClientTimeout:
            logger.error("Web search request timed out")
            return {
                "success": False,
                "error": "Request timeout",
                "message": "Web search request timed out after 30 seconds"
            }
            
        except aiohttp.ClientError as e:
            logger.error(f"Web search client error: {e}")
            return {
                "success": False,
                "error": "Network error",
                "message": f"Failed to connect to search API: {str(e)}"
            }
            
        except Exception as e:
            logger.error(f"Error in web search: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Web search failed - check system configuration"
            }
    
    async def _check_rate_limits(self) -> Dict[str, Any]:
        """Check if request is within rate limits."""
        try:
            now = datetime.now()
            window_start = now - timedelta(seconds=self.RATE_LIMIT_WINDOW)
            
            # Clean old timestamps
            self.request_timestamps = [
                ts for ts in self.request_timestamps if ts >= window_start
            ]
            
            current_requests = len(self.request_timestamps)
            
            if current_requests >= self.RATE_LIMIT_REQUESTS:
                oldest_request = min(self.request_timestamps)
                retry_after = int((oldest_request + timedelta(seconds=self.rate_limit_window) - now).total_seconds())
                
                return {
                    "allowed": False,
                    "message": f"Rate limit exceeded: {current_requests}/{self.RATE_LIMIT_REQUESTS} requests in last hour",
                    "retry_after": max(retry_after, 0)
                }
            
            return {
                "allowed": True,
                "remaining": self.RATE_LIMIT_REQUESTS - current_requests
            }
            
        except Exception as e:
            logger.error(f"Error checking rate limits: {e}")
            return {"allowed": True, "remaining": self.RATE_LIMIT_REQUESTS}
    
    async def _get_rate_limit_remaining(self) -> int:
        """Get remaining requests in current rate limit window."""
        try:
            rate_check = await self._check_rate_limits()
            return rate_check.get("remaining", 0)
        except Exception:
            return 0
    
    async def _process_search_results(
        self,
        api_response: Dict[str, Any],
        original_query: str,
        include_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """Process and format search results from LangSearch API response."""
        try:
            formatted_results = []
            
            # Extract search data
            data = api_response.get("data", {})
            web_pages = data.get("webPages", {})
            results = web_pages.get("value", [])
            
            for i, result in enumerate(results):
                # Core result data
                formatted_result = {
                    "id": result.get("id", f"result_{i}"),
                    "title": result.get("name", "Untitled"),
                    "url": result.get("url", ""),
                    "display_url": result.get("displayUrl", result.get("url", "")),
                    "snippet": result.get("snippet", ""),
                    "rank": i + 1
                }
                
                # Add summary if available
                if result.get("summary"):
                    formatted_result["summary"] = result["summary"]
                
                # Add dates if available
                if result.get("datePublished"):
                    formatted_result["date_published"] = result["datePublished"]
                if result.get("dateLastCrawled"):
                    formatted_result["date_crawled"] = result["dateLastCrawled"]
                
                # Extract domain from URL
                try:
                    from urllib.parse import urlparse
                    parsed_url = urlparse(formatted_result["url"])
                    formatted_result["domain"] = parsed_url.netloc
                    formatted_result["source"] = parsed_url.netloc
                except Exception:
                    formatted_result["domain"] = "unknown"
                    formatted_result["source"] = "unknown"
                
                # Calculate relevance score (simple heuristic based on ranking)
                max_results = len(results)
                relevance_score = max(0.0, 1.0 - (i / max_results)) if max_results > 0 else 0.0
                formatted_result["relevance_score"] = round(relevance_score, 3)
                
                # Add metadata if requested
                if include_metadata:
                    formatted_result["search_metadata"] = {
                        "query": original_query,
                        "search_engine": "LangSearch",
                        "result_position": i + 1,
                        "total_results": max_results,
                        "has_summary": bool(result.get("summary")),
                        "content_length": len(result.get("snippet", "") + result.get("summary", "")),
                        "search_timestamp": datetime.now().isoformat()
                    }
                
                formatted_results.append(formatted_result)
            
            # Sort by relevance score (highest first)
            formatted_results.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error processing search results: {e}")
            return []
    
    async def get_search_stats(self) -> Dict[str, Any]:
        """Get search tool statistics and status."""
        try:
            rate_limit_status = await self._check_rate_limits()
            
            return {
                "success": True,
                "api_status": {
                    "endpoint": self.langsearch_endpoint,
                    "api_key_configured": bool(self.api_key),
                    "api_key_preview": f"{self.api_key[:8]}..." if self.api_key else None
                },
                "rate_limiting": {
                    "requests_per_hour": self.RATE_LIMIT_REQUESTS,
                    "window_seconds": self.RATE_LIMIT_WINDOW,
                    "requests_in_current_window": len(self.request_timestamps),
                    "remaining_requests": rate_limit_status.get("remaining", 0),
                    "rate_limit_exceeded": not rate_limit_status["allowed"]
                },
                "supported_features": [
                    "Web page search",
                    "Time-based filtering (oneDay, oneWeek, oneMonth, oneYear, noLimit)",
                    "Content summaries",
                    "Source attribution",
                    "Result ranking",
                    "Rate limiting",
                    "Error handling"
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting search stats: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to retrieve search tool statistics"
            }
    
    async def call(self, operation: str, **kwargs) -> Dict[str, Any]:
        """
        Main tool call method for Mirascope integration.
        
        Args:
            operation: Operation to perform ('search', 'stats')
            **kwargs: Operation-specific parameters
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if operation == "search":
                input_data = WebSearchInput(**kwargs)
                return await self.web_search(input_data)
            
            elif operation == "stats":
                return await self.get_search_stats()
            
            else:
                return {
                    "success": False,
                    "error": f"Unknown operation: {operation}",
                    "available_operations": ["search", "stats"]
                }
                
        except Exception as e:
            logger.error(f"Error in web search tool call: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Web search tool error: {str(e)}"
            }


# Global web search tool instance
_web_search_tool = None


def get_web_search_tool() -> WebSearchTool:
    """Get the global web search tool instance (singleton pattern)."""
    global _web_search_tool
    if _web_search_tool is None:
        _web_search_tool = WebSearchTool()
    return _web_search_tool


# Export main components
__all__ = [
    "WebSearchTool",
    "WebSearchInput",
    "get_web_search_tool"
]
