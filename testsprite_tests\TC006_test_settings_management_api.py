import requests

BASE_URL = "http://localhost:3000"
TIMEOUT = 30
HEADERS = {"Content-Type": "application/json"}

def test_settings_management_api():
    # Define endpoints
    api_key_endpoint = f"{BASE_URL}/settings/api-key"
    model_endpoint = f"{BASE_URL}/settings/model"
    embedding_status_endpoint = f"{BASE_URL}/settings/embedding-status"

    # Sample test data
    new_api_key = {"api_key": "test-api-key-123456"}
    new_model = {"model_name": "gpt-4-test-model"}
    new_embedding_status = {"enabled": True}

    # Store original settings to restore after test
    original_api_key = None
    original_model = None
    original_embedding_status = None

    try:
        # 1. Get current API key
        resp = requests.get(api_key_endpoint, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        if resp.content:
            original_api_key = resp.json().get("api_key")

        # 2. Update API key
        resp = requests.put(api_key_endpoint, json=new_api_key, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        assert resp.json().get("api_key") == new_api_key["api_key"]

        # 3. Verify API key persisted by GET
        resp = requests.get(api_key_endpoint, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        assert resp.json().get("api_key") == new_api_key["api_key"]

        # 4. Get current model selection
        resp = requests.get(model_endpoint, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        if resp.content:
            original_model = resp.json().get("model_name")

        # 5. Update model selection
        resp = requests.put(model_endpoint, json=new_model, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        assert resp.json().get("model_name") == new_model["model_name"]

        # 6. Verify model selection persisted by GET
        resp = requests.get(model_endpoint, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        assert resp.json().get("model_name") == new_model["model_name"]

        # 7. Get current embedding index status
        resp = requests.get(embedding_status_endpoint, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        if resp.content:
            original_embedding_status = resp.json().get("enabled")

        # 8. Update embedding index status
        resp = requests.put(embedding_status_endpoint, json=new_embedding_status, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        assert resp.json().get("enabled") == new_embedding_status["enabled"]

        # 9. Verify embedding index status persisted by GET
        resp = requests.get(embedding_status_endpoint, headers=HEADERS, timeout=TIMEOUT)
        assert resp.status_code == 200
        assert resp.json().get("enabled") == new_embedding_status["enabled"]

    finally:
        # Restore original API key
        if original_api_key is not None:
            try:
                requests.put(api_key_endpoint, json={"api_key": original_api_key}, headers=HEADERS, timeout=TIMEOUT)
            except Exception:
                pass

        # Restore original model
        if original_model is not None:
            try:
                requests.put(model_endpoint, json={"model_name": original_model}, headers=HEADERS, timeout=TIMEOUT)
            except Exception:
                pass

        # Restore original embedding status
        if original_embedding_status is not None:
            try:
                requests.put(embedding_status_endpoint, json={"enabled": original_embedding_status}, headers=HEADERS, timeout=TIMEOUT)
            except Exception:
                pass

test_settings_management_api()