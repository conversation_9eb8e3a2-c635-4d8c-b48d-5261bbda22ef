import requests
import uuid

BASE_URL = "http://localhost:3000"
TIMEOUT = 30
HEADERS = {"Content-Type": "application/json"}

def test_settings_management_api():
    get_settings_url = f"{BASE_URL}/settings"
    update_settings_url = f"{BASE_URL}/settings"

    try:
        resp = requests.get(get_settings_url, headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
        if not resp.content or resp.content.strip() == b'':
            original_settings = {"api_key": "", "model": "", "embedding_index_enabled": False}
        else:
            try:
                original_settings = resp.json()
            except ValueError:
                original_settings = {"api_key": "", "model": "", "embedding_index_enabled": False}
    except Exception as e:
        assert False, f"Failed to get initial settings: {e}"

    # Basic validation of original settings keys
    assert isinstance(original_settings, dict), "Original settings response is not a dict"
    for key in ["api_key", "model", "embedding_index_enabled"]:
        assert key in original_settings, f"Key '{key}' missing in original settings"

    new_api_key = f"test-api-key-{uuid.uuid4()}"
    new_model = "gpt-4"
    new_embedding_status = not original_settings.get("embedding_index_enabled", False)

    updated_settings_payload = {
        "api_key": new_api_key,
        "model": new_model,
        "embedding_index_enabled": new_embedding_status
    }

    try:
        resp = requests.put(update_settings_url, json=updated_settings_payload, headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
        updated_settings = resp.json()
    except Exception as e:
        assert False, f"Failed to update settings: {e}"

    assert updated_settings.get("api_key") == new_api_key, "API key was not updated correctly"
    assert updated_settings.get("model") == new_model, "Model was not updated correctly"
    assert updated_settings.get("embedding_index_enabled") == new_embedding_status, "Embedding index status was not updated correctly"

    try:
        resp = requests.get(get_settings_url, headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
        persisted_settings = resp.json()
    except Exception as e:
        assert False, f"Failed to get settings after update: {e}"

    assert persisted_settings.get("api_key") == new_api_key, "Persisted API key does not match updated value"
    assert persisted_settings.get("model") == new_model, "Persisted model does not match updated value"
    assert persisted_settings.get("embedding_index_enabled") == new_embedding_status, "Persisted embedding index status does not match updated value"

    try:
        resp = requests.put(update_settings_url, json=original_settings, headers=HEADERS, timeout=TIMEOUT)
        resp.raise_for_status()
    except Exception as e:
        print(f"Warning: Failed to revert settings during cleanup: {e}")

test_settings_management_api()
