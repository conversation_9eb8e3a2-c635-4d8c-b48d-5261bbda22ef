{"status": "commited", "type": "backend", "scope": "codebase", "localEndpoint": "http://localhost:3000", "backendAuthType": "public", "executionArgs": {"projectName": "OneSearch", "projectPath": "C:\\Users\\<USER>\\Documents\\Coding\\OneSearch", "testIds": [], "additionalInstruction": "All test files have been updated with correct API endpoints. Please run all tests.", "envs": {"API_KEY": "sk-user-D1ny3dAkbLaN1rKtDtAZe2Yx_eR1hA1ACZtSzNkNQLFBZVWQzsLvoak-nV2RQfBMpqn09FhpuwtuuDJrsmQmylw-81X86b4BrCwg_R-E2NXnwrNaYcO7TjC12fn6EruvJ6A"}}, "proxy": "http://494b8a8f-5c33-4fa3-89b6-0467ce661690:<EMAIL>:8080"}