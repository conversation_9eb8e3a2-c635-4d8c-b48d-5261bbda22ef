import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { CheckSquare, Plus, Search, Clock, Calendar, Tag } from 'lucide-react'
import { API_ENDPOINTS } from '../config/api'

interface Task {
  id: string
  title: string
  description?: string
  category?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  due_date?: string
  created_at: string
  updated_at?: string
  completed_at?: string
  ai_generated_category?: string
  ai_confidence?: number
  ai_reasoning?: string
  ai_suggestions?: any
}

interface TaskResponse {
  success: boolean
  tasks: Task[]
  message?: string
  error?: string
}

/**
 * Tasks page component - Intelligent task management with data fetching
 */
const Tasks: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'completed'>('all')

  // Fetch tasks on component mount
  useEffect(() => {
    console.log('🚀 Tasks component mounted - useEffect running!')
    const fetchTasks = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(API_ENDPOINTS.TASKS)
        const data: TaskResponse = await response.json()
        
        if (data.success && data.tasks) {
          setTasks(data.tasks)
        } else {
          setError(data.message || 'Failed to fetch tasks')
        }
      } catch (err) {
        console.error('Error fetching tasks:', err)
        setError('Failed to connect to server')
      } finally {
        setIsLoading(false)
      }
    }

    fetchTasks()
  }, [])

  // Filter tasks based on search and status
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = searchTerm === '' || 
      task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = selectedFilter === 'all' || task.status === selectedFilter
    
    return matchesSearch && matchesFilter
  })

  // Priority color mapping
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200'
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'medium': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'low': return 'text-gray-600 bg-gray-50 border-gray-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  // Status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200'
      case 'in_progress': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'pending': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'cancelled': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="p-6 min-h-screen">
        <div className="flex items-center justify-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-blue"></div>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 min-h-screen"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Tasks</h1>
          <p className="text-text-secondary">AI-powered task management and organization</p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="button-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          Add Task
        </motion.button>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 mb-6">
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedFilter === 'all'
                ? 'bg-accent-blue text-white'
                : 'bg-background-secondary border border-border-primary text-text-secondary hover:text-text-primary'
            }`}
          >
            All ({tasks.length})
          </button>
          <button
            onClick={() => setSelectedFilter('pending')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedFilter === 'pending'
                ? 'bg-accent-blue text-white'
                : 'bg-background-secondary border border-border-primary text-text-secondary hover:text-text-primary'
            }`}
          >
            Pending ({tasks.filter(t => t.status === 'pending').length})
          </button>
          <button
            onClick={() => setSelectedFilter('completed')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedFilter === 'completed'
                ? 'bg-accent-blue text-white'
                : 'bg-background-secondary border border-border-primary text-text-secondary hover:text-text-primary'
            }`}
          >
            Completed ({tasks.filter(t => t.status === 'completed').length})
          </button>
        </div>
        
        <div className="flex-1 max-w-md relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-text-muted" />
          <input
            type="text"
            placeholder="Search tasks..."
            className="input-primary pl-10 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Error state */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Tasks List */}
      {filteredTasks.length > 0 ? (
        <div className="space-y-4">
          {filteredTasks.map((task) => (
            <motion.div
              key={task.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-background-secondary border border-border-primary rounded-xl p-6 hover:shadow-lg transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-text-primary mb-2">
                    {task.title}
                  </h3>
                  
                  {task.description && (
                    <p className="text-text-secondary mb-3 leading-relaxed">
                      {task.description}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-3 text-sm">
                    <span className={`px-2 py-1 rounded-full border text-xs font-medium ${getPriorityColor(task.priority)}`}>
                      {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                    </span>
                    
                    <span className={`px-2 py-1 rounded-full border text-xs font-medium ${getStatusColor(task.status)}`}>
                      {task.status.replace('_', ' ').charAt(0).toUpperCase() + task.status.replace('_', ' ').slice(1)}
                    </span>
                    
                    {task.category && (
                      <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-600 text-xs font-medium">
                        <Tag className="w-3 h-3 inline mr-1" />
                        {task.category}
                      </span>
                    )}
                    
                    {task.ai_confidence && (
                      <span className="text-text-muted text-xs">
                        AI Confidence: {Math.round(task.ai_confidence * 100)}%
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-4 mt-3 text-xs text-text-muted">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      Created {new Date(task.created_at).toLocaleDateString()}
                    </span>
                    
                    {task.due_date && (
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        Due {new Date(task.due_date).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        /* Empty State */
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-16"
        >
          <div className="w-20 h-20 bg-accent-blue/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <CheckSquare className="w-10 h-10 text-accent-blue" />
          </div>
          
          <h2 className="text-2xl font-semibold text-text-primary mb-4">
            {tasks.length === 0 ? 'No Tasks Yet' : 'No Matching Tasks'}
          </h2>
          
          <p className="text-text-secondary mb-6 max-w-md mx-auto">
            {tasks.length === 0 
              ? 'Start by typing something in the main dashboard. The AI will automatically create tasks for you!'
              : 'Try adjusting your search or filter settings.'
            }
          </p>
          
          {tasks.length === 0 && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.location.href = '/dashboard'}
              className="button-primary"
            >
              Go to Dashboard
            </motion.button>
          )}
        </motion.div>
      )}
    </motion.div>
  )
}

export default Tasks
