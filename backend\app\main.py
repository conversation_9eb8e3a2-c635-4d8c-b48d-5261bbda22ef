"""
Main FastAPI application for AI-Powered Dashboard.
CRITICAL: Follow ai_orchestrator_flow.mermaid EXACTLY for all AI processing.
Performance optimized with async/await, connection pooling, and caching.
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTT<PERSON>Exception, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import asyncio
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any
import json

from app.config.settings import get_settings, VISUAL_FEEDBACK_STATES, PERFORMANCE_CONSTANTS
from app.api.routes import router
from app.api.websockets import ConnectionManager
from app.database.connection import init_database, get_async_session
from app.services.embedding_service import EmbeddingService
from app.agents.orchestrator import AIOrchestrator
from app.models.pydantic_models import UserInput
from sqlalchemy import text
from app.tools.task_tool import TaskTool, CreateTaskInput
from app.tools.calendar_tool import CalendarTool, CreateEventInput
from app.tools.web_search_tool import WebSearchTool, WebSearchInput
from app.tools.database_search_tool import HybridDatabaseSearchTool, HybridSearchInput
from app.middleware.security_middleware import setup_security_middleware
from app.middleware.error_middleware import ErrorHandlingMiddleware
from app.utils.error_handling import ErrorHandler

# Configure logging for performance monitoring
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances
settings = get_settings()
connection_manager = ConnectionManager()
embedding_service = EmbeddingService()
orchestrator = AIOrchestrator()
error_handler = ErrorHandler()

# Initialize tools for real operations
task_tool = TaskTool()
calendar_tool = CalendarTool()
web_search_tool = WebSearchTool()
database_search_tool = HybridDatabaseSearchTool()

# Performance tracking metrics
performance_metrics = {
    "requests_count": 0,
    "avg_response_time": 0.0,
    "active_connections": 0,
    "memory_usage": 0,
    "cache_hits": 0,
    "cache_misses": 0
}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan events with performance optimization.
    CRITICAL: Initialize all AI services for mermaid flow compliance.
    """
    try:
        # Startup
        logger.info("🚀 Starting AI-Powered Dashboard with performance optimizations...")
        
        # Initialize database with performance settings
        logger.info("📊 Initializing database with connection pooling...")
        await init_database()
        
        # Initialize embedding service with pre-warming
        logger.info("🧮 Initializing Ollama embedding service...")
        await embedding_service.initialize()
        
        # Store services in app state for route access
        app.state.embedding_service = embedding_service
        
        logger.info("✅ Application startup complete - Ready for AI orchestration!")
        logger.info("🎭 Following ai_orchestrator_flow.mermaid for all AI processing")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("🔄 Shutting down AI-Powered Dashboard...")
        await embedding_service.cleanup()
        logger.info("✅ Application shutdown complete")


# Create FastAPI application with security optimization
app = FastAPI(
    title="AI-Powered Dashboard API",
    description="Backend API with visual transparency following ai_orchestrator_flow.mermaid",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# Setup comprehensive security middleware
environment = getattr(settings, 'environment', 'production')
from app.middleware.security import setup_security_middleware
setup_security_middleware(app, environment)

# Add error handling middleware
app.add_middleware(ErrorHandlingMiddleware, error_handler=error_handler)

# Performance middleware for request tracking
@app.middleware("http")
async def performance_middleware(request: Request, call_next):
    """Track performance metrics and add monitoring headers."""
    start_time = time.time()
    performance_metrics["requests_count"] += 1
    
    try:
        response = await call_next(request)
        
        # Calculate and track response time
        process_time = time.time() - start_time
        performance_metrics["avg_response_time"] = (
            (performance_metrics["avg_response_time"] + process_time) / 2
        )
        
        # Add performance headers for monitoring
        response.headers["X-Process-Time"] = str(round(process_time, 3))
        response.headers["X-Request-ID"] = str(performance_metrics["requests_count"])
        response.headers["X-Mermaid-Compliant"] = "true"
        
        return response
        
    except Exception as e:
        logger.error(f"Request processing failed: {e}")
        process_time = time.time() - start_time
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error", 
                "request_id": performance_metrics["requests_count"],
                "process_time": round(process_time, 3)
            }
        )

# Add middleware with performance optimizations
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routes with API versioning
app.include_router(router, prefix="/api/v1")

@app.get("/")
async def root():
    """Root endpoint with system status and mermaid compliance info."""
    return {
        "message": "🎭 AI-Powered Dashboard API",
        "status": "active",
        "visual_transparency": "enabled", 
        "mermaid_flow_compliance": True,
        "performance": {
            "requests_served": performance_metrics["requests_count"],
            "avg_response_time_ms": round(performance_metrics["avg_response_time"] * 1000, 2),
            "active_websocket_connections": performance_metrics["active_connections"]
        },
        "ai_orchestrator": "ready"
    }


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time AI processing updates.
    CRITICAL: Implements visual feedback states from ai_orchestrator_flow.mermaid EXACTLY.
    """
    await connection_manager.connect(websocket)
    performance_metrics["active_connections"] += 1
    logger.info(f"WebSocket connected. Active connections: {performance_metrics['active_connections']}")
    
    try:
        while True:
            # Wait for user input from frontend
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message.get("type") == "process_input":
                # Handle both payload formats for compatibility
                if "payload" in message and isinstance(message["payload"], dict):
                    user_input_text = message["payload"].get("input", "")
                else:
                    user_input_text = message.get("content", "")
                    
                logger.info(f"🎭 Processing user input: {user_input_text[:50]}...")
                
                try:
                    # Create UserInput model
                    user_input = UserInput(text=user_input_text)
                    
                    # STEP 1: Send initial analyzing state (per mermaid diagram)
                    feedback_state = VISUAL_FEEDBACK_STATES["analyzing"]
                    await connection_manager.send_personal_message(json.dumps({
                        "type": "processing_step",
                        "payload": {
                            "step": "analyzing",
                            "message": feedback_state["message"],
                            "animation": feedback_state["animation"], 
                            "duration": feedback_state["duration"],
                            "timestamp": time.time()
                        }
                    }), websocket)
                    
                    await asyncio.sleep(feedback_state["duration"])
                    
                    # STEP 2: Send categorizing state (per mermaid diagram)
                    feedback_state = VISUAL_FEEDBACK_STATES["categorizing"]
                    await connection_manager.send_personal_message(json.dumps({
                        "type": "processing_step",
                        "payload": {
                            "step": "categorizing", 
                            "message": feedback_state["message"],
                            "animation": feedback_state["animation"],
                            "duration": feedback_state["duration"],
                            "timestamp": time.time()
                        }
                    }), websocket)
                    
                    await asyncio.sleep(feedback_state["duration"])
                    
                    # STEP 3: ACTUAL AI CATEGORIZATION using orchestrator
                    category_decision = await orchestrator.categorize_input(user_input)
                    
                    # STEP 4: Send category-specific identification (per mermaid diagram)
                    if category_decision.category.value == "task":
                        feedback_state = VISUAL_FEEDBACK_STATES["task_identified"]
                    elif category_decision.category.value == "event":
                        feedback_state = VISUAL_FEEDBACK_STATES["event_identified"] 
                    else:  # ai_question
                        feedback_state = VISUAL_FEEDBACK_STATES["question_identified"]
                        
                    await connection_manager.send_personal_message(json.dumps({
                        "type": "processing_step",
                        "payload": {
                            "step": f"{category_decision.category.value}_identified",
                            "stepCategory": category_decision.category.value.upper(),
                            "message": feedback_state["message"],
                            "animation": feedback_state["animation"],
                            "duration": feedback_state["duration"],
                            "category": category_decision.category.value,
                            "confidence": category_decision.confidence,
                            "reasoning": category_decision.reasoning,
                            "timestamp": time.time()
                        }
                    }), websocket)
                    
                    await asyncio.sleep(feedback_state["duration"])
                    
                    # STEP 5: Process based on category (following mermaid flow)
                    logger.info(f"🔄 Routing to category-specific workflow: {category_decision.category.value}")
                    if category_decision.category.value == "task":
                        logger.info("🎯 Starting task workflow...")
                        await _process_task_workflow(user_input, category_decision, websocket)
                    elif category_decision.category.value == "event":
                        logger.info("🎯 Starting event workflow...")
                        await _process_event_workflow(user_input, category_decision, websocket)
                    else:  # ai_question
                        logger.info("🎯 Starting question workflow...")
                        await _process_question_workflow(user_input, category_decision, websocket)
                    
                    logger.info("✅ Workflow completed successfully")
                    
                except Exception as e:
                    logger.error(f"Error processing input: {e}")
                    await connection_manager.send_personal_message(json.dumps({
                        "type": "processing_error",
                        "payload": {
                            "error": str(e),
                            "message": "An error occurred while processing your input",
                            "step": "error_occurred",
                            "animation": "shake_animation",
                            "timestamp": time.time()
                        }
                    }), websocket)
                
            elif message.get("type") == "connect":
                # Handle initial connection from frontend
                client_id = message.get("payload", {}).get("clientId", "unknown")
                logger.info(f"🔌 Frontend client connected: {client_id}")
                await connection_manager.send_personal_message(json.dumps({
                    "type": "connection_confirmed",
                    "payload": {
                        "status": "connected",
                        "clientId": client_id,
                        "timestamp": time.time()
                    }
                }), websocket)
                
            elif message.get("type") == "ping":
                # Handle keepalive pings
                await connection_manager.send_personal_message(json.dumps({
                    "type": "pong",
                    "timestamp": time.time()
                }), websocket)
                
            else:
                # Handle unknown message types gracefully
                message_type = message.get("type", "unknown")
                logger.warning(f"Unknown message type received: {message_type}")
                await connection_manager.send_personal_message(json.dumps({
                    "type": "message_acknowledged",
                    "payload": {
                        "original_type": message_type,
                        "status": "received_but_not_processed",
                        "timestamp": time.time()
                    }
                }), websocket)
                
    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await connection_manager.send_personal_message(json.dumps({
                "type": "processing_error",
                "payload": {
                    "error": "Connection error occurred",
                    "message": "Connection error occurred",
                    "step": "error_occurred", 
                    "animation": "shake_animation",
                    "timestamp": time.time()
                }
            }), websocket)
        except:
            pass  # Connection already closed
    finally:
        connection_manager.disconnect(websocket)
        performance_metrics["active_connections"] -= 1
        logger.info(f"WebSocket disconnected. Active connections: {performance_metrics['active_connections']}")


async def _process_task_workflow(user_input: UserInput, category_decision, websocket: WebSocket):
    """Process task workflow following mermaid diagram exactly."""
    logger.info(f"🔧 Starting task workflow for input: {user_input.text}")
    try:
        # Step: Extracting details
        logger.info("📋 Step 1: Extracting details...")
        feedback_state = VISUAL_FEEDBACK_STATES["extracting_details"]
        logger.info(f"📤 Sending extracting_details message via WebSocket...")
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "extracting_details",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Step: Auto-categorizing
        feedback_state = VISUAL_FEEDBACK_STATES["auto_categorizing"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step", 
            "payload": {
                "step": "auto_categorizing",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Step: Checking for dates
        feedback_state = VISUAL_FEEDBACK_STATES["checking_dates"] 
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "checking_dates",
                "message": feedback_state["message"], 
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Step: Updating task list - ACTUALLY CREATE TASK
        feedback_state = VISUAL_FEEDBACK_STATES["updating_task_list"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "updating_task_list", 
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        # REAL TOOL INTEGRATION: Create actual task using TaskTool
        task_input = CreateTaskInput(
            title=user_input.text,
            description=f"Task created from user input: {user_input.text}",
            category="AI-Generated",
            # Let the AI tool determine priority based on content
        )
        
        task_result = await task_tool.create_task(task_input)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Final step: Task added successfully with real data
        feedback_state = VISUAL_FEEDBACK_STATES["task_added"]
        
        # Fix datetime and enum serialization issues
        task_data = task_result.get("task", {})
        serializable_task_data = {}
        for key, value in task_data.items():
            if hasattr(value, 'isoformat'):  # datetime object
                serializable_task_data[key] = value.isoformat()
            elif hasattr(value, 'value'):  # enum object
                serializable_task_data[key] = value.value
            else:
                serializable_task_data[key] = value
        
        created_at = task_result.get("task", {}).get("created_at")
        created_at_str = created_at.isoformat() if hasattr(created_at, 'isoformat') else created_at
        
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "task_added",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        # Wait for animation to complete then send final completion message
        await asyncio.sleep(feedback_state["duration"])
        
        # Send the completion message that frontend expects
        logger.info(f"📤 Sending task_created completion message to frontend...")
        completion_message = {
            "type": "task_created",
            "payload": {
                "type": "task",
                "success": task_result.get("success", False),
                "task_data": serializable_task_data,
                "ai_suggestions": task_result.get("ai_suggestions", {}),
                "title": user_input.text,
                "question": user_input.text,
                "category": task_result.get("task", {}).get("category", "AI-Generated"),
                "confidence": category_decision.confidence,
                "database_id": task_result.get("task", {}).get("id"),
                "created_at": created_at_str,
                "result": {
                    "type": "task",
                    "success": task_result.get("success", False),
                    "task_data": serializable_task_data,
                    "confidence": category_decision.confidence,
                },
                "timestamp": time.time()
            }
        }
        logger.info(f"📋 Completion message data: {completion_message}")
        await connection_manager.send_personal_message(json.dumps(completion_message), websocket)
        logger.info(f"✅ task_created message sent successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error in task workflow: {e}")
        logger.error(f"❌ Error type: {type(e).__name__}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        # Send error message to frontend
        try:
            await connection_manager.send_personal_message(json.dumps({
                "type": "processing_error",
                "payload": {
                    "error": f"Task processing failed: {str(e)}",
                    "step": "task_workflow_error",
                    "timestamp": time.time()
                }
            }), websocket)
        except Exception as send_error:
            logger.error(f"❌ Failed to send error message: {send_error}")
        raise


async def _process_event_workflow(user_input: UserInput, category_decision, websocket: WebSocket):
    """Process event workflow following mermaid diagram exactly.""" 
    try:
        # Step: Adding to calendar - ACTUALLY CREATE EVENT
        feedback_state = VISUAL_FEEDBACK_STATES["adding_to_calendar"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "adding_to_calendar",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        # REAL TOOL INTEGRATION: Create actual calendar event using CalendarTool
        event_input = CreateEventInput(
            title=user_input.text,
            description=f"Event created from user input: {user_input.text}",
            # Let the AI tool parse and suggest timing from user input
            suggested_date="today",  # Tool will intelligently parse from user input
        )
        
        event_result = await calendar_tool.create_event(event_input)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Final step: Event scheduled successfully with real data
        feedback_state = VISUAL_FEEDBACK_STATES["event_scheduled"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "event_scheduled",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        # Wait for animation to complete then send final completion message
        await asyncio.sleep(feedback_state["duration"])
        
        # Send the completion message that frontend expects
        await connection_manager.send_personal_message(json.dumps({
            "type": "event_created",
            "payload": {
                "type": "event",
                "success": event_result.get("success", False),
                "event_data": event_result.get("event", {}),
                "ai_suggestions": event_result.get("ai_suggestions", {}),
                "title": user_input.text,
                "question": user_input.text,
                "confidence": category_decision.confidence,
                "database_id": event_result.get("event", {}).get("id"),
                "start_time": event_result.get("event", {}).get("start_time"),
                "end_time": event_result.get("event", {}).get("end_time"),
                "created_at": event_result.get("event", {}).get("created_at"),
                "result": {
                    "type": "event",
                    "success": event_result.get("success", False),
                    "event_data": event_result.get("event", {}),
                    "confidence": category_decision.confidence,
                },
                "timestamp": time.time()
            }
        }), websocket)
        
    except Exception as e:
        logger.error(f"Error in event workflow: {e}")
        raise


async def _process_question_workflow(user_input: UserInput, category_decision, websocket: WebSocket):
    """Process question workflow following mermaid diagram exactly."""
    try:
        # Step: Searching database - ACTUAL DATABASE SEARCH
        feedback_state = VISUAL_FEEDBACK_STATES["searching_database"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "searching_database",
                "message": feedback_state["message"], 
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        # REAL TOOL INTEGRATION: Search database using HybridDatabaseSearchTool
        db_search_input = HybridSearchInput(
            query=user_input.text,
            search_type="all",  # Search all types: tasks, events, inputs
            search_mode="hybrid",  # Use hybrid search for best results
            limit=10
        )
        
        db_search_result = await database_search_tool.hybrid_search(db_search_input)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Step: Generating embeddings - ACTUAL WEB SEARCH IF DB HAS NO RESULTS
        feedback_state = VISUAL_FEEDBACK_STATES["generating_embeddings"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "generating_embeddings",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"], 
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        # Perform web search if database didn't have sufficient results
        web_search_result = None
        if not db_search_result.get("results") or len(db_search_result.get("results", [])) < 3:
            web_search_input = WebSearchInput(
                query=user_input.text,
                freshness="oneWeek",  # Get recent results
                max_results=5
            )
            web_search_result = await web_search_tool.web_search(web_search_input)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Step: Vector searching - COMBINING RESULTS
        feedback_state = VISUAL_FEEDBACK_STATES["vector_searching"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step", 
            "payload": {
                "step": "vector_searching",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        await asyncio.sleep(feedback_state["duration"])
        
        # Final step: Answer ready with REAL COMBINED RESULTS
        feedback_state = VISUAL_FEEDBACK_STATES["answer_ready"]
        await connection_manager.send_personal_message(json.dumps({
            "type": "processing_step",
            "payload": {
                "step": "answer_ready",
                "message": feedback_state["message"],
                "animation": feedback_state["animation"],
                "duration": feedback_state["duration"],
                "timestamp": time.time()
            }
        }), websocket)
        
        # Wait for animation to complete then send final completion message
        await asyncio.sleep(feedback_state["duration"])
        
        # Send the completion message that frontend expects  
        await connection_manager.send_personal_message(json.dumps({
            "type": "search_results",
            "payload": {
                "type": "answer",
                "question": user_input.text,
                "confidence": category_decision.confidence,
                "database_results": db_search_result.get("results", []),
                "web_results": web_search_result.get("results", []) if web_search_result else [],
                "combined_answer": {
                    "sources": "database" if db_search_result.get("results") else "web_search",
                    "result_count": len(db_search_result.get("results", [])) + (len(web_search_result.get("results", [])) if web_search_result else 0),
                    "database_hit": bool(db_search_result.get("results")),
                    "web_search_used": bool(web_search_result),
                },
                "ai_analysis": db_search_result.get("ai_analysis", {}),
                "result": {
                    "type": "answer",
                    "question": user_input.text,
                    "confidence": category_decision.confidence,
                    "database_results": db_search_result.get("results", []),
                    "web_results": web_search_result.get("results", []) if web_search_result else [],
                    "combined_answer": {
                        "sources": "database" if db_search_result.get("results") else "web_search",
                        "result_count": len(db_search_result.get("results", [])) + (len(web_search_result.get("results", [])) if web_search_result else 0),
                        "database_hit": bool(db_search_result.get("results")),
                        "web_search_used": bool(web_search_result),
                    },
                },
                "timestamp": time.time()
            }
        }), websocket)
        
    except Exception as e:
        logger.error(f"Error in question workflow: {e}")
        raise


@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint for monitoring and Docker."""
    try:
        # Check database connection with timeout
        async_session_factory = get_async_session()
        async with async_session_factory() as session:
            await session.execute(text("SELECT 1"))
        
        # Check embedding service health
        embedding_healthy = embedding_service.initialized
        
        return {
            "status": "healthy",
            "database": "connected",
            "embedding_service": "healthy" if embedding_healthy else "degraded",
            "mermaid_compliance": True,
            "performance_metrics": {
                "requests_served": performance_metrics["requests_count"],
                "avg_response_time_ms": round(performance_metrics["avg_response_time"] * 1000, 2),
                "active_websocket_connections": performance_metrics["active_connections"],
                "cache_hit_ratio": (
                    performance_metrics["cache_hits"] / 
                    max(performance_metrics["cache_hits"] + performance_metrics["cache_misses"], 1)
                )
            },
            "timestamp": time.time(),
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )

@app.get("/api/v1/performance")
async def get_performance_metrics():
    """Get detailed performance metrics for monitoring and optimization."""
    if not settings.debug:
        raise HTTPException(status_code=404, detail="Not found")
    
    return {
        "performance_metrics": performance_metrics,
        "animation_constants": PERFORMANCE_CONSTANTS["ANIMATION_INTERVALS"],
        "visual_feedback_states": list(VISUAL_FEEDBACK_STATES.keys()),
        "mermaid_flow_compliance": True,
        "database_performance": {
            "pool_size": settings.db_pool_size,
            "max_overflow": settings.db_max_overflow,
            "pool_timeout": settings.db_pool_timeout
        },
        "cache_settings": {
            "embedding_cache_enabled": settings.enable_embedding_cache,
            "llm_cache_enabled": settings.enable_llm_cache,
            "cache_ttl_seconds": settings.cache_ttl
        }
    }


if __name__ == "__main__":
    # Production-ready server configuration with performance optimization
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        workers=settings.workers or 1,
        log_level=settings.log_level.lower(),
        access_log=True,
        use_colors=True,
        loop="asyncio",
        # Performance optimizations
        timeout_keep_alive=30,
        timeout_notify=30,
        limit_concurrency=1000,
        limit_max_requests=10000,
    )
