import requests

BASE_URL = "http://localhost:3000"
TIMEOUT = 30
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_search_agent_routing_and_response_format():
    """
    Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly,
    returning relevance-ranked results with source attribution and expandable answer panels.
    """
    # Example AI question input to trigger search agent routing
    payload = {
        "query": "What are the latest advancements in AI-powered dashboards?"
    }

    try:
        response = requests.post(
            f"{BASE_URL}/api/search-agent/query",
            json=payload,
            headers=HEADERS,
            timeout=TIMEOUT
        )
        response.raise_for_status()
    except requests.RequestException as e:
        assert False, f"Request to Search Agent API failed: {e}"

    data = response.json()

    # Validate top-level response structure
    assert isinstance(data, dict), "Response should be a JSON object"
    assert "results" in data, "Response missing 'results' key"
    assert isinstance(data["results"], list), "'results' should be a list"
    assert len(data["results"]) > 0, "'results' list should not be empty"

    # Validate each result item
    for result in data["results"]:
        assert isinstance(result, dict), "Each result should be a dictionary"
        # Required fields for relevance-ranked results with source attribution
        assert "title" in result and isinstance(result["title"], str) and result["title"], "Result missing valid 'title'"
        assert "snippet" in result and isinstance(result["snippet"], str), "Result missing valid 'snippet'"
        assert "relevance_score" in result and isinstance(result["relevance_score"], (int, float)), "Result missing valid 'relevance_score'"
        assert 0 <= result["relevance_score"] <= 1, "'relevance_score' must be between 0 and 1"
        assert "source" in result and isinstance(result["source"], dict), "Result missing valid 'source' attribution"
        # Source attribution should include at least a name and a URL
        source = result["source"]
        assert "name" in source and isinstance(source["name"], str) and source["name"], "Source missing valid 'name'"
        assert "url" in source and isinstance(source["url"], str) and source["url"].startswith("http"), "Source missing valid 'url'"

        # Check for expandable answer panel presence (optional field but if present must be string)
        if "answer_panel" in result:
            assert isinstance(result["answer_panel"], str), "'answer_panel' must be a string if present"

    # Validate that results are sorted by relevance_score descending
    relevance_scores = [r["relevance_score"] for r in data["results"]]
    assert relevance_scores == sorted(relevance_scores, reverse=True), "Results are not sorted by relevance_score descending"

test_search_agent_routing_and_response_format()