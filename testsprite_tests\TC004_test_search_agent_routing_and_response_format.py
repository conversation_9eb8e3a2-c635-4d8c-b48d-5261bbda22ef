import requests

BASE_URL = "http://localhost:3000"
TIMEOUT = 30
HEADERS = {
    "Content-Type": "application/json"
}

def test_search_agent_routing_and_response_format():
    """
    Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly,
    returning relevance-ranked results with source attribution and expandable answer panels.
    """
    # Example AI question input to trigger search routing
    payload = {
        "query": "What are the latest advancements in AI-powered dashboards?"
    }

    try:
        response = requests.post(
            f"{BASE_URL}/api/search-agent/search",
            json=payload,
            headers=HEADERS,
            timeout=TIMEOUT
        )
        response.raise_for_status()
    except requests.RequestException as e:
        assert False, f"Request to Search Agent API failed: {e}"

    data = response.json()

    # Validate response structure
    assert isinstance(data, dict), "Response should be a JSON object"
    assert "results" in data, "Response must contain 'results' key"
    results = data["results"]
    assert isinstance(results, list), "'results' should be a list"
    assert len(results) > 0, "Results list should not be empty"

    # Validate each result item
    for item in results:
        assert isinstance(item, dict), "Each result should be a dictionary"
        # Check required keys for relevance-ranked results with source attribution and expandable answer panels
        assert "title" in item and isinstance(item["title"], str) and item["title"], "Result must have a non-empty 'title'"
        assert "snippet" in item and isinstance(item["snippet"], str), "Result must have a 'snippet' string"
        assert "source" in item and isinstance(item["source"], str) and item["source"], "Result must have a non-empty 'source'"
        assert "relevance_score" in item and isinstance(item["relevance_score"], (float, int)), "Result must have a numeric 'relevance_score'"
        assert 0 <= item["relevance_score"] <= 1, "'relevance_score' must be between 0 and 1"
        # Optional expandable answer panel content
        if "answer_panel" in item:
            assert isinstance(item["answer_panel"], dict), "'answer_panel' must be a dictionary if present"
            # Check for expected keys inside answer_panel if present
            assert "content" in item["answer_panel"], "'answer_panel' must contain 'content' key"
            assert isinstance(item["answer_panel"]["content"], str), "'content' in answer_panel must be a string"

    # Validate results are sorted by relevance_score descending
    relevance_scores = [item["relevance_score"] for item in results]
    assert relevance_scores == sorted(relevance_scores, reverse=True), "Results must be sorted by relevance_score descending"

test_search_agent_routing_and_response_format()