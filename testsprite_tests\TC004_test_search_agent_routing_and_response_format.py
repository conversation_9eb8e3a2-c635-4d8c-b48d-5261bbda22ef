import requests

BASE_URL = "http://localhost:3000"
TIMEOUT = 30
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_search_agent_routing_and_response_format():
    """
    Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly,
    returning relevance-ranked results with source attribution and expandable answer panels.
    """
    # Example AI question input to trigger search agent routing
    payload = {
        "query": "What are the latest advancements in AI-powered dashboards?"
    }

    try:
        response = requests.post(
            f"{BASE_URL}/api/search",
            json=payload,
            headers=HEADERS,
            timeout=TIMEOUT
        )
        assert response.status_code == 200, f"Search request failed with status {response.status_code}: {response.text}"
    except requests.RequestException as e:
        assert False, f"Request to Search API failed: {e}"

    data = response.json()

    # Validate top-level response structure based on actual API
    assert isinstance(data, dict), "Response should be a JSON object"
    assert "success" in data, "Response missing 'success' key"
    assert data["success"] == True, "Search request was not successful"
    assert "results" in data, "Response missing 'results' key"
    assert isinstance(data["results"], list), "'results' should be a list"
    assert len(data["results"]) > 0, "'results' list should not be empty"

    # Validate each result item
    for result in data["results"]:
        assert isinstance(result, dict), "Each result should be a dictionary"
        # Check for basic result structure - the exact fields may vary based on search type
        assert "source" in result, "Result missing 'source' field"
        source_type = result["source"]
        assert source_type in ["database", "web"], f"Invalid source type: {source_type}"

        # For web results, check for typical web search fields
        if source_type == "web":
            assert "title" in result or "url" in result, "Web result missing title or url"

        # For database results, check for database-specific fields
        if source_type == "database":
            assert "type" in result or "content" in result, "Database result missing type or content"

test_search_agent_routing_and_response_format()