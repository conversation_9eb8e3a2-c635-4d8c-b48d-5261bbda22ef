import requests
import time

BASE_URL = "http://localhost:3000"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30

def test_data_persistence_across_sessions():
    # Step 1: Submit arbitrary input to AI Orchestrator endpoint
    input_payload = {
        "input_text": "Schedule a meeting with the team next Friday at 3pm"
    }
    create_resp = None
    try:
        create_resp = requests.post(
            f"{BASE_URL}/api/orchestrator/process",
            json=input_payload,
            headers=HEADERS,
            timeout=TIMEOUT
        )
        assert create_resp.status_code == 200, f"Unexpected status code: {create_resp.status_code}"
        create_data = create_resp.json()
        # Validate response contains category and confidence > 0.7
        assert "category" in create_data, "Response missing 'category'"
        assert "confidence" in create_data, "Response missing 'confidence'"
        assert create_data["confidence"] > 0.7, f"Confidence too low: {create_data['confidence']}"
        resource_id = create_data.get("resource_id")
        assert resource_id is not None, "Response missing 'resource_id' for created resource"

        # Step 2: Simulate page refresh / new session by waiting and re-fetching the resource
        time.sleep(2)  # simulate delay between sessions

        # Step 3: Retrieve the created resource to verify persistence
        get_resp = requests.get(
            f"{BASE_URL}/api/orchestrator/resource/{resource_id}",
            headers=HEADERS,
            timeout=TIMEOUT
        )
        assert get_resp.status_code == 200, f"Failed to retrieve resource: {get_resp.status_code}"
        get_data = get_resp.json()

        # Validate the retrieved data matches the originally submitted data and AI categorization
        assert get_data.get("input_text") == input_payload["input_text"], "Input text mismatch on retrieval"
        assert get_data.get("category") == create_data["category"], "Category mismatch on retrieval"
        assert abs(get_data.get("confidence", 0) - create_data["confidence"]) < 0.001, "Confidence mismatch on retrieval"

        # Step 4: Simulate another session by re-fetching again after some time
        time.sleep(2)
        get_resp_2 = requests.get(
            f"{BASE_URL}/api/orchestrator/resource/{resource_id}",
            headers=HEADERS,
            timeout=TIMEOUT
        )
        assert get_resp_2.status_code == 200, f"Failed to retrieve resource second time: {get_resp_2.status_code}"
        get_data_2 = get_resp_2.json()

        # Validate persistence again
        assert get_data_2 == get_data, "Data not persistent across sessions"

    finally:
        # Cleanup: delete the created resource if possible
        if create_resp is not None and create_resp.status_code == 200:
            try:
                requests.delete(
                    f"{BASE_URL}/api/orchestrator/resource/{resource_id}",
                    headers=HEADERS,
                    timeout=TIMEOUT
                )
            except Exception:
                pass

test_data_persistence_across_sessions()