import requests
import time

BASE_URL = "http://localhost:3000"
TIMEOUT = 30
HEADERS = {"Content-Type": "application/json"}

def test_data_persistence_across_sessions():
    # Step 1: Submit arbitrary input to AI Orchestrator endpoint
    input_payload = {
        "input_text": "Schedule a meeting with the team next Friday at 3pm"
    }
    create_resp = requests.post(
        f"{BASE_URL}/api/orchestrator",
        json=input_payload,
        headers=HEADERS,
        timeout=TIMEOUT,
    )
    assert create_resp.status_code == 200, f"Unexpected status code: {create_resp.status_code}"
    create_data = create_resp.json()
    assert "category" in create_data, "Response missing 'category'"
    assert create_data.get("confidence", 0) > 0.7, "Confidence score too low"
    resource_id = create_data.get("resource_id")
    assert resource_id, "Response missing resource_id for created resource"

    # Determine correct resource path based on category
    category = create_data.get("category")
    if category == "Task":
        resource_path = "tasks"
    elif category == "Event":
        resource_path = "events"
    elif category == "AI Question":
        resource_path = "questions"
    else:
        raise AssertionError(f"Unknown category returned: {category}")

    try:
        # Step 2: Retrieve the created resource (task or event or question) to verify persistence
        get_resp = requests.get(
            f"{BASE_URL}/api/{resource_path}/{resource_id}",
            headers=HEADERS,
            timeout=TIMEOUT,
        )
        assert get_resp.status_code == 200, f"Failed to retrieve resource {resource_id}"
        get_data = get_resp.json()
        assert get_data.get("input_text") == input_payload["input_text"], "Input text mismatch on retrieval"
        assert get_data.get("category") == create_data.get("category"), "Category mismatch on retrieval"

        # Step 3: Simulate page refresh / new session by waiting and re-fetching the resource
        time.sleep(2)  # simulate delay between sessions

        get_resp_2 = requests.get(
            f"{BASE_URL}/api/{resource_path}/{resource_id}",
            headers=HEADERS,
            timeout=TIMEOUT,
        )
        assert get_resp_2.status_code == 200, f"Failed to retrieve resource on second fetch {resource_id}"
        get_data_2 = get_resp_2.json()
        assert get_data_2 == get_data, "Data mismatch between sessions, persistence failed"

        # Step 4: Verify that the backend SQLite WAL mode and embedding storage are implied by persistence
        # (No direct API to check WAL mode, so persistence of data across sessions is the proxy)

    finally:
        # Cleanup: Delete the created resource to keep test environment clean
        del_resp = requests.delete(
            f"{BASE_URL}/api/{resource_path}/{resource_id}",
            headers=HEADERS,
            timeout=TIMEOUT,
        )
        assert del_resp.status_code in (200, 204), f"Failed to delete resource {resource_id}"

test_data_persistence_across_sessions()
