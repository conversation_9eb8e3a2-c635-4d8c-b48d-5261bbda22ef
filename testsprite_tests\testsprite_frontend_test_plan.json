[{"id": "TC001", "title": "Hero Input Bar accepts arbitrary text inputs and AI categorizes correctly", "description": "Verify that the Hero Input Bar accepts any arbitrary text input and the AI Orchestrator categorizes it into Task, Event, or AI Question with a confidence score above 0.7 without relying on keyword hardcoding.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the dashboard page with the hero input bar visible"}, {"type": "action", "description": "Enter various arbitrary text inputs including normal, ambiguous, and complex sentences"}, {"type": "action", "description": "Submit each input via the hero input bar"}, {"type": "assertion", "description": "Verify that the AI Orchestrator returns a category from Task, Event, or AI Question"}, {"type": "assertion", "description": "Verify that the confidence score for the categorization is greater than 0.7 for each input"}, {"type": "assertion", "description": "Confirm categorization is consistent without the use of keyword hardcoding"}]}, {"id": "TC002", "title": "Visual feedback animations correspond exactly to AI processing workflow", "description": "Verify that the visual animations for AI processing show the exact sequence of steps as defined by the mermaid diagram workflow, including analyzing input, categorizing, identification, and completion stages with smooth 60fps animations.", "category": "ui", "priority": "High", "steps": [{"type": "action", "description": "Enter a valid input in the hero input bar and submit"}, {"type": "assertion", "description": "Observe the animation sequence and verify it matches the prescribed workflow stages exactly: Analyzing input -> Categorizing -> Identification -> Completion"}, {"type": "assertion", "description": "Confirm all animations run smoothly at 60fps with spring physics and GPU acceleration (transform3d)"}, {"type": "assertion", "description": "Verify no skipped or out-of-order animation steps occur"}]}, {"id": "TC003", "title": "Task Management: Auto-categorized tasks appear in smart list with priority indicators", "description": "Validate that inputs categorized as Tasks automatically appear in the task list, show AI-assigned priority color coding, properly handle due dates, and support CRUD operations with real-time UI updates.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Submit a text input categorized as a Task"}, {"type": "assertion", "description": "Verify the new task appears in the smart task list UI"}, {"type": "assertion", "description": "Check that the AI-assigned priority is indicated with the correct color coding"}, {"type": "action", "description": "Edit the task's description, priority, and due date"}, {"type": "assertion", "description": "Confirm the edits reflect immediately in the UI"}, {"type": "action", "description": "Mark the task as complete"}, {"type": "assertion", "description": "Check task completion status updates in UI and persists"}, {"type": "action", "description": "Reorder tasks via drag-and-drop"}, {"type": "assertion", "description": "Verify task order updates and persists correctly"}]}, {"id": "TC004", "title": "Calendar events creation, viewing, updates, and conflict detection", "description": "Test that the Calendar Agent processes event inputs correctly, events appear accurately in UI calendar views (month, week, day), support drag-and-drop rescheduling, and detect conflicts dynamically.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Submit an input categorized as an Event with a specific date/time"}, {"type": "assertion", "description": "Verify the event appears correctly in all available calendar views"}, {"type": "action", "description": "Create events manually via the calendar UI"}, {"type": "assertion", "description": "Confirm event details save and display correctly"}, {"type": "action", "description": "Drag and drop events to new dates/times"}, {"type": "assertion", "description": "Ensure updates persist and are reflected visually immediately"}, {"type": "action", "description": "Attempt to create overlapping events"}, {"type": "assertion", "description": "Verify conflict detection triggers appropriate UI warnings or resolutions"}, {"type": "action", "description": "Delete an event via the calendar UI"}, {"type": "assertion", "description": "Confirm the event is removed from the calendar and database"}]}, {"id": "TC005", "title": "Search Agent AI question answering with semantic and web search, result ranking and sources", "description": "Verify that questions categorized as AI Questions are routed correctly to semantic and LangSearch web search tools, that results are combined with relevance scoring, and that source links and expandable answer panels display all information properly.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Submit an AI question via the hero input bar"}, {"type": "assertion", "description": "Verify the Search Agent dispatches the query to semantic database search and LangSearch web search"}, {"type": "assertion", "description": "Confirm combined results are relevance-ranked in the UI"}, {"type": "assertion", "description": "Check that source attribution links are visible for each result"}, {"type": "action", "description": "Expand and collapse answer panels in the UI"}, {"type": "assertion", "description": "Ensure expand/collapse transitions are smooth and content loads correctly"}]}, {"id": "TC006", "title": "State persistence across page reloads with localStorage and SQLite WAL mode", "description": "Ensure that user inputs, task and event data, and AI processing state persist seamlessly across page reloads using localStorage and SQLite with WAL mode, maintaining consistency without requiring user login.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Enter multiple inputs categorizing into tasks, events, and questions"}, {"type": "action", "description": "Perform updates on tasks and calendar events"}, {"type": "action", "description": "Reload/refresh the application page"}, {"type": "assertion", "description": "Verify all previously entered inputs, tasks, events, and AI states are restored accurately"}, {"type": "assertion", "description": "Confirm no data loss or corruption occurs"}]}, {"id": "TC007", "title": "WebSocket streaming stability and reconnection logic", "description": "Test that the WebSocket connection between frontend and backend remains stable during AI processing streaming, supports real-time UI updates, and correctly reconnects after simulated connection failures.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Start a hero input submission triggering AI orchestration with WebSocket streaming"}, {"type": "assertion", "description": "Observe that AI processing steps stream in real-time and UI updates accordingly"}, {"type": "action", "description": "Simulate WebSocket connection drop during streaming"}, {"type": "assertion", "description": "Verify that reconnection attempts occur automatically"}, {"type": "assertion", "description": "Confirm streaming resumes and UI updates continue after reconnection"}]}, {"id": "TC008", "title": "Backend API endpoints support full CRUD and secure operation", "description": "Validate that backend FastAPI endpoints support full CRUD operations for tasks, events, settings management, and that security middleware enforces input validation and rate limiting to prevent abuse.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Perform create, read, update, and delete operations via API for tasks and events"}, {"type": "assertion", "description": "Verify successful responses and data consistency for each operation"}, {"type": "action", "description": "Submit invalid or malformed data to endpoints"}, {"type": "assertion", "description": "Confirm input validations reject invalid data with appropriate error messages"}, {"type": "action", "description": "Execute rapid repeated API calls exceeding rate limits"}, {"type": "assertion", "description": "Check that rate limiting middleware restricts the calls and returns proper status codes"}]}, {"id": "TC009", "title": "Error handling and user-friendly feedback for AI processing and connectivity issues", "description": "Test comprehensive error handling by simulating AI model failures, API errors, and connection interruptions, verifying that the system provides graceful user messages and retry mechanisms.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Induce a simulated AI processing failure (e.g. model timeout or rejection)"}, {"type": "assertion", "description": "Verify user sees a meaningful error message explaining the issue"}, {"type": "action", "description": "Trigger backend API errors by sending invalid requests"}, {"type": "assertion", "description": "Confirm the UI handles error responses gracefully without crashes"}, {"type": "action", "description": "Simulate lost connection between frontend and backend"}, {"type": "assertion", "description": "Verify that retry mechanisms attempt reconnect and inform the user of connectivity status"}]}, {"id": "TC010", "title": "Docker containerization and startup with zero errors", "description": "Verify that the entire system including backend, frontend, and embedding services runs fully contained in Docker containers, with successful startup and passing integration tests without errors.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Build and start backend, frontend, and embedding service Docker containers using provided Dockerfiles"}, {"type": "assertion", "description": "Confirm containers initialize without errors or crashes in logs"}, {"type": "assertion", "description": "Verify backend and frontend services are accessible and responsive"}, {"type": "action", "description": "Run full integration test suite against deployed containers"}, {"type": "assertion", "description": "Confirm all integration tests pass successfully without failures"}]}, {"id": "TC011", "title": "Performance monitoring and optimization under typical workload", "description": "Assess performance monitoring tools by simulating typical user workload to verify optimized response times, low resource usage, and correct metric reporting on frontend and backend.", "category": "performance", "priority": "Medium", "steps": [{"type": "action", "description": "Simulate typical user interactions including input submissions, task/calendar management, and AI queries"}, {"type": "assertion", "description": "Verify response times stay within acceptable performance thresholds"}, {"type": "assertion", "description": "Monitor CPU, memory, and network usage remain optimized and stable"}, {"type": "assertion", "description": "Check performance monitoring dashboards or logs for correct and meaningful data"}]}, {"id": "TC012", "title": "Security validations against injection and other attacks", "description": "Test that security middleware effectively sanitizes inputs and enforces protections against injection attacks and other malicious payloads, verified through targeted attack simulations.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Submit inputs containing SQL injections, script injections (XSS), and malformed payloads via UI and API endpoints"}, {"type": "assertion", "description": "Ensure inputs are sanitized or rejected appropriately by backend"}, {"type": "assertion", "description": "Confirm application remains stable and no unauthorized execution or data leakage occurs"}, {"type": "action", "description": "Test API endpoints against typical attack vectors such as command injection and path traversal"}, {"type": "assertion", "description": "Verify all security middleware blocks or neutralizes these attacks"}]}, {"id": "TC013", "title": "Settings UI: API keys, model selection, embedding status configuration", "description": "Verify that the settings management UI allows users to configure API keys, select AI models, and view embedding index statuses, with changes persisting and reflected in AI processing behaviors.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Navigate to the settings UI page"}, {"type": "action", "description": "Input new API keys and save"}, {"type": "assertion", "description": "Verify changes persist and are securely stored"}, {"type": "action", "description": "Select different AI models from dropdown and save"}, {"type": "assertion", "description": "Confirm model selection updates take effect on subsequent AI orchestrations"}, {"type": "assertion", "description": "Check embedding index status indicators accurately reflect service status"}]}, {"id": "TC014", "title": "UI animations use Framer Motion with spring physics and GPU acceleration for smooth transitions", "description": "Test that all UI animations, including hero input feedback, task and calendar updates, and settings changes, are implemented with Framer Motion, running at consistent 60fps using spring physics and GPU acceleration (transform3d, will-change).", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Trigger various UI transitions: submitting inputs, task edits, calendar event drag-drop, settings changes"}, {"type": "assertion", "description": "Measure animation frame rate and smoothness confirming consistent 60fps"}, {"type": "assertion", "description": "Confirm animations exhibit spring physics behavior"}, {"type": "assertion", "description": "Use browser devtools to verify GPU acceleration CSS properties (transform3d, will-change) are applied"}]}]