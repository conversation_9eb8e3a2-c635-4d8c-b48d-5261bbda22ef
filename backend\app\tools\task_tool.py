"""
Task management tool for AI-Powered Dashboard.

This tool provides CRUD operations for tasks with AI-powered categorization
following research/mirascope/page6-tools.md for tool implementation.
"""

import asyncio
import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta, date
from pydantic import BaseModel, Field, validator

from mirascope.core.base.tool import BaseTool
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload
from dateutil import parser as date_parser

from app.database.connection import get_db_session, DatabaseTransaction
from app.database.models import Task as TaskModel, ProcessingLog
from app.models.pydantic_models import TaskData, TaskPriority, TaskStatus
from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CreateTaskInput(BaseModel):
    """Input model for creating tasks."""
    title: str = Field(..., description="Task title or description")
    description: Optional[str] = Field(None, description="Detailed task description")
    category: Optional[str] = Field(None, description="Task category (will be AI-generated if not provided)")
    priority: Optional[TaskPriority] = Field(None, description="Task priority level")
    due_date: Optional[str] = Field(None, description="Due date as natural language (e.g., 'tomorrow', 'next Friday')")
    estimated_duration: Optional[int] = Field(None, description="Estimated duration in minutes")
    tags: Optional[List[str]] = Field(None, description="Task tags for organization")

    @validator('title')
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError("Task title cannot be empty")
        return v.strip()


class UpdateTaskInput(BaseModel):
    """Input model for updating tasks."""
    task_id: int = Field(..., description="ID of task to update")
    title: Optional[str] = Field(None, description="New task title")
    description: Optional[str] = Field(None, description="New task description")
    category: Optional[str] = Field(None, description="New task category")
    priority: Optional[TaskPriority] = Field(None, description="New task priority")
    due_date: Optional[str] = Field(None, description="New due date as natural language")
    estimated_duration: Optional[int] = Field(None, description="New estimated duration in minutes")
    tags: Optional[List[str]] = Field(None, description="New task tags")
    status: Optional[TaskStatus] = Field(None, description="New task status")
    progress_percentage: Optional[int] = Field(None, description="Progress percentage (0-100)")

    @validator('progress_percentage')
    def validate_progress(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError("Progress percentage must be between 0 and 100")
        return v


class SearchTasksInput(BaseModel):
    """Input model for searching tasks."""
    search_text: Optional[str] = Field(None, description="Search in title/description")
    category: Optional[str] = Field(None, description="Filter by category")
    priority: Optional[TaskPriority] = Field(None, description="Filter by priority")
    status: Optional[TaskStatus] = Field(None, description="Filter by status")
    due_before: Optional[str] = Field(None, description="Tasks due before this date")
    due_after: Optional[str] = Field(None, description="Tasks due after this date")
    tags: Optional[List[str]] = Field(None, description="Filter by tags (any match)")
    overdue_only: bool = Field(False, description="Show only overdue tasks")
    completed: Optional[bool] = Field(None, description="Filter by completion status")
    limit: int = Field(50, description="Maximum number of results")


class BulkOperationInput(BaseModel):
    """Input model for bulk task operations."""
    task_ids: List[int] = Field(..., description="List of task IDs to operate on")
    operation: str = Field(..., description="Operation: 'complete', 'delete', 'update_priority', 'update_category'")
    new_priority: Optional[TaskPriority] = Field(None, description="New priority for bulk update")
    new_category: Optional[str] = Field(None, description="New category for bulk update")
    new_status: Optional[TaskStatus] = Field(None, description="New status for bulk update")


class TaskTool(BaseTool):
    """
    Mirascope tool for task operations with AI-powered categorization.
    
    This tool provides comprehensive task management functionality
    with intelligent categorization and priority assignment.
    """
    
    def __init__(self):
        super().__init__()
    
    async def create_task(self, input_data: CreateTaskInput) -> Dict[str, Any]:
        """
        Create a new task with AI-powered categorization and priority assignment.
        
        Args:
            input_data: Task creation parameters
            
        Returns:
            Dict[str, Any]: Created task data with AI suggestions
        """
        try:
            logger.info(f"Creating task: {input_data.title}")
            
            # Parse due date if provided
            parsed_due_date = None
            if input_data.due_date:
                parsed_due_date = await self._parse_due_date(input_data.due_date)
                if not parsed_due_date:
                    return {
                        "success": False,
                        "error": f"Could not parse due date: {input_data.due_date}",
                        "suggestions": [
                            "Try formats like: 'tomorrow', 'next Friday', 'in 3 days', 'Dec 25'",
                            "Be more specific with times if needed"
                        ]
                    }
            
            # AI-powered categorization if not provided
            ai_category = input_data.category
            if not ai_category:
                ai_category = await self._generate_category(input_data.title, input_data.description)
            
            # AI-powered priority assignment if not provided
            ai_priority = input_data.priority
            if not ai_priority:
                ai_priority = await self._assign_priority(
                    input_data.title, 
                    input_data.description,
                    parsed_due_date
                )
            
            async with DatabaseTransaction() as session:
                # Create new task model
                task_model = TaskModel(
                    title=input_data.title,
                    description=input_data.description,
                    category=ai_category,
                    priority=ai_priority.value,
                    due_date=parsed_due_date,
                    status=TaskStatus.PENDING.value,
                    # Add AI metadata to the existing fields
                    ai_confidence=0.85,  # Mock confidence - would come from LLM in real implementation
                    ai_reasoning=f"AI categorized as '{ai_category}' with priority '{ai_priority.value}'"
                )
                
                session.add(task_model)
                await session.flush()  # Get ID without committing
                
                # Convert to TaskData for response
                task_data = TaskData(
                    id=str(task_model.id),
                    title=task_model.title,
                    description=task_model.description,
                    category=task_model.category,
                    priority=TaskPriority(task_model.priority),
                    due_date=task_model.due_date,
                    estimated_duration=input_data.estimated_duration,  # Use input data for fields not in DB
                    tags=input_data.tags or [],  # Use input data for fields not in DB
                    status=TaskStatus(task_model.status),
                    progress_percentage=0,  # Default value for fields not in DB
                    created_at=task_model.created_at,
                    ai_generated_category=ai_category if not input_data.category else None,
                    ai_generated_priority=ai_priority.value if not input_data.priority else None
                )
                
                # Generate AI suggestions for similar tasks
                suggestions = await self._generate_suggestions(task_model.title, task_model.category)
                
                logger.info(f"Task created successfully: ID {task_model.id}")
                
                return {
                    "success": True,
                    "task": task_data.dict(),
                    "ai_insights": {
                        "category_generated": not input_data.category,
                        "priority_generated": not input_data.priority,
                        "category_confidence": 0.85,  # Mock confidence - in real impl would come from LLM
                        "priority_reasoning": await self._get_priority_reasoning(ai_priority, parsed_due_date)
                    },
                    "suggestions": suggestions,
                    "message": f"Task '{input_data.title}' created successfully in '{ai_category}' category"
                }
                
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create task"
            }
    
    async def update_task(self, input_data: UpdateTaskInput) -> Dict[str, Any]:
        """
        Update an existing task.
        
        Args:
            input_data: Task update parameters
            
        Returns:
            Dict[str, Any]: Updated task data
        """
        try:
            logger.info(f"Updating task: {input_data.task_id}")
            
            async with DatabaseTransaction() as session:
                # Find existing task
                result = await session.execute(
                    select(TaskModel).where(TaskModel.id == input_data.task_id)
                )
                task_model = result.scalar_one_or_none()
                
                if not task_model:
                    return {
                        "success": False,
                        "error": "Task not found",
                        "message": f"Task with ID {input_data.task_id} not found"
                    }
                
                # Parse new due date if provided
                parsed_due_date = None
                if input_data.due_date:
                    parsed_due_date = await self._parse_due_date(input_data.due_date)
                    if not parsed_due_date:
                        return {
                            "success": False,
                            "error": f"Could not parse due date: {input_data.due_date}"
                        }
                
                # Update fields that are provided
                changes_made = []
                if input_data.title is not None:
                    task_model.title = input_data.title
                    changes_made.append("title")
                if input_data.description is not None:
                    task_model.description = input_data.description
                    changes_made.append("description")
                if input_data.category is not None:
                    task_model.category = input_data.category
                    changes_made.append("category")
                if input_data.priority is not None:
                    task_model.priority = input_data.priority.value
                    changes_made.append("priority")
                if parsed_due_date is not None:
                    task_model.due_date = parsed_due_date
                    changes_made.append("due_date")
                if input_data.estimated_duration is not None:
                    task_model.estimated_duration = input_data.estimated_duration
                    changes_made.append("estimated_duration")
                if input_data.tags is not None:
                    task_model.tags = input_data.tags
                    changes_made.append("tags")
                if input_data.status is not None:
                    task_model.status = input_data.status.value
                    changes_made.append("status")
                if input_data.progress_percentage is not None:
                    task_model.progress_percentage = input_data.progress_percentage
                    changes_made.append("progress")
                    
                    # Auto-complete if progress reaches 100%
                    if input_data.progress_percentage >= 100:
                        task_model.status = TaskStatus.COMPLETED.value
                        task_model.completed_at = datetime.now()
                        changes_made.append("auto_completed")
                
                task_model.updated_at = datetime.now()
                
                # Convert to TaskData for response
                task_data = TaskData(
                    id=str(task_model.id),
                    title=task_model.title,
                    description=task_model.description,
                    category=task_model.category,
                    priority=TaskPriority(task_model.priority),
                    due_date=task_model.due_date,
                    estimated_duration=task_model.estimated_duration,
                    tags=task_model.tags or [],
                    status=TaskStatus(task_model.status),
                    progress_percentage=task_model.progress_percentage,
                    created_at=task_model.created_at,
                    updated_at=task_model.updated_at,
                    completed_at=task_model.completed_at,
                    ai_generated_category=task_model.ai_generated_category,
                    ai_generated_priority=task_model.ai_generated_priority
                )
                
                logger.info(f"Task updated successfully: ID {input_data.task_id}")
                
                return {
                    "success": True,
                    "task": task_data.dict(),
                    "changes_made": changes_made,
                    "message": f"Task updated successfully ({', '.join(changes_made)})"
                }
                
        except Exception as e:
            logger.error(f"Error updating task: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to update task"
            }
    
    async def delete_task(self, task_id: int) -> Dict[str, Any]:
        """
        Delete a task.
        
        Args:
            task_id: ID of task to delete
            
        Returns:
            Dict[str, Any]: Deletion result
        """
        try:
            logger.info(f"Deleting task: {task_id}")
            
            async with DatabaseTransaction() as session:
                # Find and delete task
                result = await session.execute(
                    select(TaskModel).where(TaskModel.id == task_id)
                )
                task_model = result.scalar_one_or_none()
                
                if not task_model:
                    return {
                        "success": False,
                        "error": "Task not found",
                        "message": f"Task with ID {task_id} not found"
                    }
                
                task_title = task_model.title
                await session.delete(task_model)
                
                logger.info(f"Task deleted successfully: ID {task_id}")
                
                return {
                    "success": True,
                    "message": f"Task '{task_title}' deleted successfully"
                }
                
        except Exception as e:
            logger.error(f"Error deleting task: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to delete task"
            }
    
    async def search_tasks(self, input_data: SearchTasksInput) -> Dict[str, Any]:
        """
        Search for tasks with various filters.
        
        Args:
            input_data: Search parameters
            
        Returns:
            Dict[str, Any]: Search results
        """
        try:
            logger.info(f"Searching tasks with filters: {input_data.dict()}")
            
            async with DatabaseTransaction() as session:
                # Build query
                query = select(TaskModel)
                conditions = []
                
                # Text search
                if input_data.search_text:
                    search_term = f"%{input_data.search_text}%"
                    conditions.append(
                        or_(
                            TaskModel.title.ilike(search_term),
                            TaskModel.description.ilike(search_term),
                            TaskModel.category.ilike(search_term)
                        )
                    )
                
                # Category filter
                if input_data.category:
                    conditions.append(TaskModel.category == input_data.category)
                
                # Priority filter
                if input_data.priority:
                    conditions.append(TaskModel.priority == input_data.priority.value)
                
                # Status filter
                if input_data.status:
                    conditions.append(TaskModel.status == input_data.status.value)
                
                # Completion filter
                if input_data.completed is not None:
                    if input_data.completed:
                        conditions.append(TaskModel.status == TaskStatus.COMPLETED.value)
                    else:
                        conditions.append(TaskModel.status != TaskStatus.COMPLETED.value)
                
                # Due date filters
                if input_data.due_before:
                    due_before = await self._parse_due_date(input_data.due_before)
                    if due_before:
                        conditions.append(TaskModel.due_date <= due_before)
                
                if input_data.due_after:
                    due_after = await self._parse_due_date(input_data.due_after)
                    if due_after:
                        conditions.append(TaskModel.due_date >= due_after)
                
                # Overdue filter
                if input_data.overdue_only:
                    now = datetime.now()
                    conditions.append(
                        and_(
                            TaskModel.due_date < now,
                            TaskModel.status != TaskStatus.COMPLETED.value
                        )
                    )
                
                # Tags filter
                if input_data.tags:
                    # PostgreSQL array overlap operator - for SQLite we'll use JSON containment
                    for tag in input_data.tags:
                        conditions.append(TaskModel.tags.contains([tag]))
                
                # Apply conditions
                if conditions:
                    query = query.where(and_(*conditions))
                
                # Order by priority and due date
                query = query.order_by(
                    TaskModel.due_date.asc().nullslast(),
                    TaskModel.priority.asc(),
                    TaskModel.created_at.desc()
                ).limit(input_data.limit)
                
                # Execute query
                result = await session.execute(query)
                task_models = result.scalars().all()
                
                # Convert to TaskData objects
                tasks = []
                for task_model in task_models:
                    task_data = TaskData(
                        id=str(task_model.id),
                        title=task_model.title,
                        description=task_model.description,
                        category=task_model.category,
                        priority=TaskPriority(task_model.priority),
                        due_date=task_model.due_date,
                        status=TaskStatus(task_model.status),
                        created_at=task_model.created_at,
                        updated_at=task_model.updated_at,
                        completed_at=task_model.completed_at,
                        ai_generated_category=getattr(task_model, 'ai_generated_category', None),
                        ai_confidence=getattr(task_model, 'ai_confidence', None),
                        ai_reasoning=getattr(task_model, 'ai_reasoning', None),
                        ai_suggestions=getattr(task_model, 'ai_suggestions', None)
                    )
                    tasks.append(task_data.dict())
                
                logger.info(f"Found {len(tasks)} tasks matching search criteria")
                
                return {
                    "success": True,
                    "tasks": tasks,
                    "count": len(tasks),
                    "message": f"Found {len(tasks)} tasks"
                }
                
        except Exception as e:
            logger.error(f"Error searching tasks: {e}")
            return {
                "success": False,
                "error": str(e),
                "tasks": [],
                "count": 0,
                "message": "Failed to search tasks"
            }
    
    async def bulk_operation(self, input_data: BulkOperationInput) -> Dict[str, Any]:
        """
        Perform bulk operations on multiple tasks.
        
        Args:
            input_data: Bulk operation parameters
            
        Returns:
            Dict[str, Any]: Bulk operation results
        """
        try:
            logger.info(f"Performing bulk {input_data.operation} on {len(input_data.task_ids)} tasks")
            
            async with DatabaseTransaction() as session:
                # Find all tasks
                result = await session.execute(
                    select(TaskModel).where(TaskModel.id.in_(input_data.task_ids))
                )
                tasks = result.scalars().all()
                
                if not tasks:
                    return {
                        "success": False,
                        "error": "No tasks found with provided IDs",
                        "processed": 0
                    }
                
                processed_count = 0
                errors = []
                
                for task in tasks:
                    try:
                        if input_data.operation == "complete":
                            task.status = TaskStatus.COMPLETED.value
                            task.progress_percentage = 100
                            task.completed_at = datetime.now()
                            
                        elif input_data.operation == "delete":
                            await session.delete(task)
                            
                        elif input_data.operation == "update_priority":
                            if input_data.new_priority:
                                task.priority = input_data.new_priority.value
                                
                        elif input_data.operation == "update_category":
                            if input_data.new_category:
                                task.category = input_data.new_category
                                
                        elif input_data.operation == "update_status":
                            if input_data.new_status:
                                task.status = input_data.new_status.value
                                if input_data.new_status == TaskStatus.COMPLETED:
                                    task.progress_percentage = 100
                                    task.completed_at = datetime.now()
                        
                        task.updated_at = datetime.now()
                        processed_count += 1
                        
                    except Exception as e:
                        errors.append(f"Task {task.id}: {str(e)}")
                
                logger.info(f"Bulk operation completed: {processed_count} tasks processed")
                
                return {
                    "success": True,
                    "processed": processed_count,
                    "total": len(input_data.task_ids),
                    "errors": errors,
                    "message": f"Bulk {input_data.operation} completed: {processed_count} tasks processed"
                }
                
        except Exception as e:
            logger.error(f"Error in bulk operation: {e}")
            return {
                "success": False,
                "error": str(e),
                "processed": 0,
                "message": "Failed to perform bulk operation"
            }
    
    async def get_task_analytics(self) -> Dict[str, Any]:
        """
        Get task analytics and statistics.
        
        Returns:
            Dict[str, Any]: Task analytics data
        """
        try:
            async with DatabaseTransaction() as session:
                # Count tasks by status
                status_result = await session.execute(
                    select(TaskModel.status, func.count(TaskModel.id))
                    .group_by(TaskModel.status)
                )
                status_counts = dict(status_result.fetchall())
                
                # Count tasks by priority
                priority_result = await session.execute(
                    select(TaskModel.priority, func.count(TaskModel.id))
                    .group_by(TaskModel.priority)
                )
                priority_counts = dict(priority_result.fetchall())
                
                # Count tasks by category
                category_result = await session.execute(
                    select(TaskModel.category, func.count(TaskModel.id))
                    .group_by(TaskModel.category)
                    .limit(10)
                )
                category_counts = dict(category_result.fetchall())
                
                # Count overdue tasks
                now = datetime.now()
                overdue_result = await session.execute(
                    select(func.count(TaskModel.id))
                    .where(
                        and_(
                            TaskModel.due_date < now,
                            TaskModel.status != TaskStatus.COMPLETED.value
                        )
                    )
                )
                overdue_count = overdue_result.scalar() or 0
                
                # Count tasks due today
                today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                today_end = today_start + timedelta(days=1)
                today_result = await session.execute(
                    select(func.count(TaskModel.id))
                    .where(
                        and_(
                            TaskModel.due_date >= today_start,
                            TaskModel.due_date < today_end,
                            TaskModel.status != TaskStatus.COMPLETED.value
                        )
                    )
                )
                today_count = today_result.scalar() or 0
                
                return {
                    "success": True,
                    "analytics": {
                        "status_distribution": status_counts,
                        "priority_distribution": priority_counts,
                        "category_distribution": category_counts,
                        "overdue_tasks": overdue_count,
                        "due_today": today_count,
                        "total_tasks": sum(status_counts.values())
                    },
                    "message": "Task analytics retrieved successfully"
                }
                
        except Exception as e:
            logger.error(f"Error getting task analytics: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to get task analytics"
            }
    
    async def _parse_due_date(self, date_str: str) -> Optional[datetime]:
        """Parse natural language due date string."""
        if not date_str or not date_str.strip():
            return None
            
        date_str = date_str.strip().lower()
        now = datetime.now()
        
        try:
            # Handle relative dates
            if date_str in ['today']:
                return now.replace(hour=23, minute=59, second=59)
            elif date_str in ['tomorrow']:
                return (now + timedelta(days=1)).replace(hour=23, minute=59, second=59)
            elif date_str.startswith('in ') and 'day' in date_str:
                # "in 3 days", "in 1 day"
                match = re.search(r'in (\d+) days?', date_str)
                if match:
                    days = int(match.group(1))
                    return (now + timedelta(days=days)).replace(hour=23, minute=59, second=59)
            elif 'next week' in date_str:
                days_until_monday = (7 - now.weekday()) % 7
                if days_until_monday == 0:  # If today is Monday
                    days_until_monday = 7
                return (now + timedelta(days=days_until_monday + 7)).replace(hour=23, minute=59, second=59)
            
            # Use dateutil for more complex parsing
            parsed = date_parser.parse(date_str, fuzzy=True)
            
            # If parsed date is in the past, assume user means future
            if parsed < now:
                if parsed.date() == now.date():
                    # Same day but past time - set to end of day
                    parsed = parsed.replace(hour=23, minute=59, second=59)
                else:
                    # Past date - assume next occurrence
                    parsed = parsed + timedelta(days=7)
            
            return parsed
            
        except (ValueError, TypeError):
            logger.warning(f"Could not parse date string: {date_str}")
            return None
    
    async def _generate_category(self, title: str, description: Optional[str]) -> str:
        """
        Generate AI-powered category for task.
        In a real implementation, this would use an LLM.
        """
        # Simple heuristic categorization - in real implementation use LLM
        text = f"{title} {description or ''}".lower()
        
        if any(word in text for word in ['work', 'meeting', 'project', 'deadline', 'presentation']):
            return "Work"
        elif any(word in text for word in ['study', 'homework', 'exam', 'class', 'learn']):
            return "Education"
        elif any(word in text for word in ['buy', 'shopping', 'grocery', 'store']):
            return "Shopping"
        elif any(word in text for word in ['health', 'doctor', 'exercise', 'gym', 'run']):
            return "Health"
        elif any(word in text for word in ['clean', 'wash', 'organize', 'tidy', 'fix']):
            return "Household"
        elif any(word in text for word in ['call', 'email', 'contact', 'message']):
            return "Communication"
        elif any(word in text for word in ['bill', 'pay', 'money', 'budget', 'finance']):
            return "Finance"
        else:
            return "General"
    
    async def _assign_priority(self, title: str, description: Optional[str], due_date: Optional[datetime]) -> TaskPriority:
        """
        AI-powered priority assignment based on content and due date.
        In a real implementation, this would use an LLM.
        """
        text = f"{title} {description or ''}".lower()
        
        # Check for urgent keywords
        if any(word in text for word in ['urgent', 'asap', 'emergency', 'critical', 'important']):
            return TaskPriority.URGENT
        
        # Check due date proximity
        if due_date:
            now = datetime.now()
            days_until_due = (due_date - now).days
            
            if days_until_due <= 0:  # Overdue
                return TaskPriority.URGENT
            elif days_until_due <= 1:  # Due tomorrow or today
                return TaskPriority.HIGH
            elif days_until_due <= 3:  # Due within 3 days
                return TaskPriority.MEDIUM
        
        # Check for high priority keywords
        if any(word in text for word in ['deadline', 'due', 'meeting', 'presentation', 'interview']):
            return TaskPriority.HIGH
        
        # Default to medium priority
        return TaskPriority.MEDIUM
    
    async def _get_priority_reasoning(self, priority: TaskPriority, due_date: Optional[datetime]) -> str:
        """Get reasoning for priority assignment."""
        if priority == TaskPriority.URGENT:
            if due_date and due_date <= datetime.now():
                return "Marked as urgent due to overdue status"
            return "Marked as urgent due to critical keywords in description"
        elif priority == TaskPriority.HIGH:
            if due_date:
                days = (due_date - datetime.now()).days
                if days <= 1:
                    return f"High priority due to approaching deadline ({days} days)"
            return "High priority due to important keywords or context"
        elif priority == TaskPriority.MEDIUM:
            return "Medium priority assigned as balanced default"
        else:
            return "Low priority for non-urgent tasks"
    
    async def _generate_suggestions(self, title: str, category: str) -> List[str]:
        """Generate AI suggestions for similar or related tasks."""
        # Simple suggestion generator - in real implementation use LLM
        suggestions = []
        
        if category.lower() == "work":
            suggestions = [
                f"Consider breaking down '{title}' into smaller subtasks",
                "Set up a calendar reminder for important deadlines",
                "Review similar work tasks for best practices"
            ]
        elif category.lower() == "education":
            suggestions = [
                "Create a study schedule with specific time blocks",
                "Gather all necessary materials before starting",
                "Consider forming a study group for complex topics"
            ]
        else:
            suggestions = [
                f"Add specific details to make '{title}' more actionable",
                "Consider setting a realistic timeline",
                "Break complex tasks into smaller, manageable steps"
            ]
        
        return suggestions[:3]  # Return top 3 suggestions
    
    async def call(self, operation: str, **kwargs) -> Dict[str, Any]:
        """
        Main tool call method for Mirascope integration.
        
        Args:
            operation: Operation to perform
            **kwargs: Operation-specific parameters
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if operation == "create":
                input_data = CreateTaskInput(**kwargs)
                return await self.create_task(input_data)
            
            elif operation == "update":
                input_data = UpdateTaskInput(**kwargs)
                return await self.update_task(input_data)
            
            elif operation == "delete":
                task_id = kwargs.get("task_id")
                if not task_id:
                    return {"success": False, "error": "task_id required for delete operation"}
                return await self.delete_task(task_id)
            
            elif operation == "search":
                input_data = SearchTasksInput(**kwargs)
                return await self.search_tasks(input_data)
            
            elif operation == "bulk":
                input_data = BulkOperationInput(**kwargs)
                return await self.bulk_operation(input_data)
            
            elif operation == "analytics":
                return await self.get_task_analytics()
            
            else:
                return {
                    "success": False,
                    "error": f"Unknown operation: {operation}",
                    "available_operations": ["create", "update", "delete", "search", "bulk", "analytics"]
                }
                
        except Exception as e:
            logger.error(f"Error in task tool call: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Task tool error: {str(e)}"
            }


# Global task tool instance
_task_tool = None


def get_task_tool() -> TaskTool:
    """Get the global task tool instance (singleton pattern)."""
    global _task_tool
    if _task_tool is None:
        _task_tool = TaskTool()
    return _task_tool


# Export main components
__all__ = [
    "TaskTool",
    "CreateTaskInput",
    "UpdateTaskInput",
    "SearchTasksInput",
    "BulkOperationInput",
    "get_task_tool"
]
