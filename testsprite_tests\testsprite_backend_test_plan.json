[{"id": "TC001", "title": "test_ai_orchestrator_input_categorization", "description": "Verify that the AI Orchestrator API correctly categorizes arbitrary user inputs into Task, Event, or AI Question with confidence scores above 0.7 without relying on keyword hardcoding."}, {"id": "TC002", "title": "test_task_management_crud_operations", "description": "Validate the Task Management API endpoints for creating, reading, updating, and deleting tasks, ensuring AI-generated categories and priority color coding are correctly applied and persisted."}, {"id": "TC003", "title": "test_calendar_event_management_and_conflict_detection", "description": "Test the Calendar API endpoints for event creation, viewing, updating, and deletion, including intelligent date parsing and conflict detection functionality."}, {"id": "TC004", "title": "test_search_agent_routing_and_response_format", "description": "Ensure the Search Agent API routes AI questions to semantic database search or LangSearch web search tools correctly, returning relevance-ranked results with source attribution and expandable answer panels."}, {"id": "TC005", "title": "test_websocket_streaming_ai_processing_updates", "description": "Verify that the WebSocket API supports stable real-time streaming of AI processing updates, including reconnection logic on failures and correct sequence of visual feedback steps."}, {"id": "TC006", "title": "test_settings_management_api", "description": "Validate the Settings Management API endpoints for configuring API keys, model selection, and embedding index status, ensuring changes persist and reflect correctly in the system."}, {"id": "TC007", "title": "test_error_handling_and_retry_mechanisms", "description": "Test the API endpoints for robust error handling and retry mechanisms, ensuring graceful user feedback is provided for invalid inputs, API failures, and connection issues."}, {"id": "TC008", "title": "test_security_middleware_rate_limiting_and_input_validation", "description": "Verify that the security middleware correctly enforces rate limiting and input validation on all API endpoints to prevent abuse and injection attacks."}, {"id": "TC009", "title": "test_data_persistence_across_sessions", "description": "Ensure that data submitted through API endpoints persists reliably across page refreshes and sessions using SQLite with WAL mode and embedding storage."}, {"id": "TC010", "title": "test_performance_monitoring_api_responses", "description": "Validate that performance monitoring tools track API response times and resource usage accurately under typical workloads."}]