import React from 'react'
import { motion } from 'framer-motion'
import HeroInputBar from '@/components/dashboard/HeroInputBar'
import VisualFeedback from '@/components/dashboard/VisualFeedback'
import { AIResponsePanel } from '@/components/ai-response/AIResponsePanel'
import { TaskList } from '@/components/tasks/TaskList'
import { useAIOrchestrator } from '@/hooks/useAIOrchestrator'
import { useAppStore } from '@/stores/appStore'

/**
 * Dashboard page component - Main AI-powered interface
 */
const Dashboard: React.FC = () => {
  const { processingResults, category } = useAIOrchestrator()
  const { isProcessing: appIsProcessing } = useAppStore()
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="min-h-screen bg-background-primary"
    >
      {/* Hero section with input bar */}
      <div className="relative min-h-[60vh] flex items-center justify-center px-6">
        <div className="w-full max-w-4xl mx-auto text-center">
          {/* Welcome header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-12"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-text-primary mb-4">
              AI-Powered
              <span className="gradient-text ml-3">Dashboard</span>
            </h1>
            <p className="text-lg md:text-xl text-text-secondary max-w-2xl mx-auto">
              Tell me anything - I'll intelligently categorize it into tasks, events, or questions 
              and handle it with the right AI tools.
            </p>
          </motion.div>

          {/* Hero Input Bar - Main Feature */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-8"
          >
            <HeroInputBar />
          </motion.div>

          {/* Visual Feedback System */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <VisualFeedback />
          </motion.div>

          {/* Results Display Section - CRITICAL MISSING COMPONENT */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-8"
          >
            {/* AI Response Panel for Questions */}
            {processingResults && category === 'AI_QUESTION' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className="mb-6"
              >
                <AIResponsePanel
                  response={{
                    type: processingResults.result?.type || 'text',
                    content: processingResults.result?.combined_answer?.result_count 
                      ? `Found ${processingResults.result.combined_answer.result_count} relevant results. ${
                          processingResults.result.database_results?.length > 0 
                            ? `Database search found ${processingResults.result.database_results.length} entries. `
                            : ''
                        }${
                          processingResults.result.web_results?.length > 0
                            ? `Web search found ${processingResults.result.web_results.length} sources.`
                            : ''
                        }`
                      : processingResults.result?.question || 'Processing completed successfully.',
                    sources: [
                      ...(processingResults.result?.database_results?.map((r: any) => r.source) || []),
                      ...(processingResults.result?.web_results?.map((r: any) => r.url) || [])
                    ],
                    searchResults: [
                      ...(processingResults.result?.database_results || []),
                      ...(processingResults.result?.web_results || [])
                    ],
                    confidence: processingResults.result?.confidence || 0.8
                  }}
                  onCopy={(content) => {
                    navigator.clipboard.writeText(content)
                    console.log('Copied to clipboard:', content)
                  }}
                />
              </motion.div>
            )}

            {/* Task Creation Confirmation */}
            {processingResults && category === 'TASK' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className="mb-6"
              >
                <div className="bg-accent-green/10 border border-accent-green/20 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-accent-green/20 rounded-full flex items-center justify-center">
                      <span className="text-accent-green text-xl">✓</span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-text-primary">Task Created Successfully!</h3>
                      <p className="text-text-secondary text-sm">
                        AI processed: "{processingResults.result?.title || processingResults.result?.question}"
                      </p>
                    </div>
                  </div>
                  {processingResults.result?.task_data && (
                    <div className="bg-background-secondary/50 rounded-lg p-4 space-y-2">
                      <div><strong>Title:</strong> {processingResults.result.task_data.title}</div>
                      {processingResults.result.task_data.description && (
                        <div><strong>Description:</strong> {processingResults.result.task_data.description}</div>
                      )}
                      {processingResults.result.task_data.category && (
                        <div><strong>AI Category:</strong> {processingResults.result.task_data.category}</div>
                      )}
                      {processingResults.result.task_data.priority && (
                        <div><strong>Priority:</strong> {processingResults.result.task_data.priority}</div>
                      )}
                      {processingResults.result.task_data.due_date && (
                        <div><strong>Due Date:</strong> {new Date(processingResults.result.task_data.due_date).toLocaleDateString()}</div>
                      )}
                    </div>
                  )}
                  <div className="mt-4 text-text-muted text-sm">
                    Confidence: {Math.round((processingResults.result?.confidence || 0.8) * 100)}%
                    {processingResults.result?.database_id && (
                      <span className="ml-4">Database ID: {processingResults.result.database_id}</span>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {/* Event Creation Confirmation */}
            {processingResults && category === 'EVENT' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className="mb-6"
              >
                <div className="bg-accent-blue/10 border border-accent-blue/20 rounded-xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-accent-blue/20 rounded-full flex items-center justify-center">
                      <span className="text-accent-blue text-xl">📅</span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-text-primary">Event Scheduled Successfully!</h3>
                      <p className="text-text-secondary text-sm">
                        AI processed: "{processingResults.result?.title || processingResults.result?.question}"
                      </p>
                    </div>
                  </div>
                  {processingResults.result?.event_data && (
                    <div className="bg-background-secondary/50 rounded-lg p-4 space-y-2">
                      <div><strong>Title:</strong> {processingResults.result.event_data.title}</div>
                      {processingResults.result.event_data.description && (
                        <div><strong>Description:</strong> {processingResults.result.event_data.description}</div>
                      )}
                      {processingResults.result.event_data.start_time && (
                        <div><strong>Start Time:</strong> {new Date(processingResults.result.event_data.start_time).toLocaleString()}</div>
                      )}
                      {processingResults.result.event_data.end_time && (
                        <div><strong>End Time:</strong> {new Date(processingResults.result.event_data.end_time).toLocaleString()}</div>
                      )}
                      {processingResults.result.event_data.location && (
                        <div><strong>Location:</strong> {processingResults.result.event_data.location}</div>
                      )}
                    </div>
                  )}
                  <div className="mt-4 text-text-muted text-sm">
                    Confidence: {Math.round((processingResults.result?.confidence || 0.8) * 100)}%
                    {processingResults.result?.database_id && (
                      <span className="ml-4">Database ID: {processingResults.result.database_id}</span>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {/* Live Task List for TASK category */}
            {(category === 'TASK' || processingResults?.result?.type === 'task') && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="mt-6"
              >
                <TaskList />
              </motion.div>
            )}
          </motion.div>

          {/* Quick examples */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-12"
          >
            <p className="text-text-muted text-sm mb-4">Try examples like:</p>
            <div className="flex flex-wrap justify-center gap-3">
              {[
                "Schedule a meeting with the team tomorrow at 3pm",
                "Create a task to review the quarterly report",
                "What's the weather like in New York?",
                "Remind me to call mom on Friday",
                "Search for React hooks best practices"
              ].map((example, index) => (
                <motion.button
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-2 bg-background-secondary hover:bg-background-hover border border-border-primary rounded-xl text-text-secondary hover:text-text-primary text-sm transition-all duration-200"
                  onClick={() => {
                    // This would trigger the input bar with the example
                    const event = new CustomEvent('setHeroInput', { detail: example })
                    window.dispatchEvent(event)
                  }}
                >
                  "{example}"
                </motion.button>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-accent-blue/5 rounded-full blur-3xl" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-purple/5 rounded-full blur-3xl" />
        </div>
      </div>

      {/* Recent activity section */}
      <div className="px-6 pb-12">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="text-2xl font-bold text-text-primary mb-6"
          >
            Recent Activity
          </motion.h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Recent Tasks */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="card-primary p-6"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-accent-blue/10 rounded-lg flex items-center justify-center">
                  <span className="text-accent-blue">✓</span>
                </div>
                <h3 className="font-semibold text-text-primary">Recent Tasks</h3>
              </div>
              <p className="text-text-secondary text-sm mb-4">
                AI-categorized tasks from your recent inputs
              </p>
              <div className="space-y-2">
                {processingResults && category === 'TASK' ? (
                  <div className="bg-accent-blue/5 rounded-lg p-3">
                    <div className="text-text-primary text-sm font-medium">
                      {processingResults.result?.task_data?.title || processingResults.result?.question || 'New task created'}
                    </div>
                    <div className="text-text-muted text-xs mt-1">
                      Just now • AI Confidence: {Math.round((processingResults.result?.confidence || 0.8) * 100)}%
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="text-text-muted text-sm">No recent tasks</div>
                    <div className="text-text-muted text-xs">
                      Try "Do math homework tomorrow"
                    </div>
                  </>
                )}
              </div>
            </motion.div>

            {/* Recent Events */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="card-primary p-6"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-accent-green/10 rounded-lg flex items-center justify-center">
                  <span className="text-accent-green">📅</span>
                </div>
                <h3 className="font-semibold text-text-primary">Recent Events</h3>
              </div>
              <p className="text-text-secondary text-sm mb-4">
                Smart calendar events from your inputs
              </p>
              <div className="space-y-2">
                {processingResults && category === 'EVENT' ? (
                  <div className="bg-accent-green/5 rounded-lg p-3">
                    <div className="text-text-primary text-sm font-medium">
                      {processingResults.result?.event_data?.title || processingResults.result?.question || 'New event scheduled'}
                    </div>
                    <div className="text-text-muted text-xs mt-1">
                      Just now • AI Confidence: {Math.round((processingResults.result?.confidence || 0.8) * 100)}%
                    </div>
                    {processingResults.result?.event_data?.start_time && (
                      <div className="text-text-secondary text-xs mt-1">
                        📅 {new Date(processingResults.result.event_data.start_time).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                ) : (
                  <>
                    <div className="text-text-muted text-sm">No recent events</div>
                    <div className="text-text-muted text-xs">
                      Try "Meeting tomorrow at 3pm"
                    </div>
                  </>
                )}
              </div>
            </motion.div>

            {/* AI Insights */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="card-primary p-6"
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-accent-purple/10 rounded-lg flex items-center justify-center">
                  <span className="text-accent-purple">🧠</span>
                </div>
                <h3 className="font-semibold text-text-primary">AI Insights</h3>
              </div>
              <p className="text-text-secondary text-sm mb-4">
                Intelligent analysis and suggestions
              </p>
              <div className="space-y-2">
                {processingResults && category === 'AI_QUESTION' ? (
                  <div className="bg-accent-purple/5 rounded-lg p-3">
                    <div className="text-text-primary text-sm font-medium">
                      Question answered: "{processingResults.result?.question?.slice(0, 50)}..."
                    </div>
                    <div className="text-text-muted text-xs mt-1">
                      Just now • Sources: {
                        (processingResults.result?.database_results?.length || 0) + 
                        (processingResults.result?.web_results?.length || 0)
                      }
                    </div>
                    <div className="text-text-secondary text-xs mt-1">
                      {processingResults.result?.combined_answer?.database_hit ? '🗄️ Database' : '🌐 Web Search'}
                    </div>
                  </div>
                ) : appIsProcessing ? (
                  <div className="bg-accent-blue/5 rounded-lg p-3">
                    <div className="text-text-primary text-sm font-medium flex items-center gap-2">
                      <div className="w-3 h-3 bg-accent-blue rounded-full animate-pulse"></div>
                      AI is processing...
                    </div>
                    <div className="text-text-muted text-xs mt-1">
                      Analyzing and categorizing your input
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="text-text-muted text-sm">Ready for input</div>
                    <div className="text-text-muted text-xs">
                      Try "What's the weather in NYC?"
                    </div>
                  </>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default Dashboard
