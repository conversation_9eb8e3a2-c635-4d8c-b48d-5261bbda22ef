"""
Database connection and session management for AI-Powered Dashboard.

This module provides SQLite connection with WAL mode for concurrent access,
following best practices from the research documentation.
"""

import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy import text, event
from typing import AsyncGenerator
import logging

from app.config.settings import get_settings
from app.database.base import Base

# Import all models to register them with SQLAlchemy metadata
from app.database.models import (
    UserInput, Task, Event, AIResponse, EmbeddingIndex, ProcessingLog
)

logger = logging.getLogger(__name__)
settings = get_settings()


# Global engine and session factory
engine = None
async_session = None


def _enable_wal_mode(dbapi_connection, connection_record):
    """Enable WAL mode for SQLite for concurrent read/write access."""
    if 'sqlite' in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        # Enable WAL mode for better concurrency
        cursor.execute("PRAGMA journal_mode=WAL")
        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys=ON")
        # Set synchronous mode for performance
        cursor.execute("PRAGMA synchronous=NORMAL")
        # Set cache size (negative value means KB)
        cursor.execute("PRAGMA cache_size=-64000")  # 64MB cache
        # Set temp store in memory
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.close()


def get_engine():
    """Get the async database engine."""
    global engine
    if engine is None:
        # Convert standard sqlite URL to async version
        database_url = settings.database_url
        if database_url.startswith('sqlite://'):
            database_url = database_url.replace('sqlite://', 'sqlite+aiosqlite://')
        
        # SQLite-specific configuration (no connection pooling)
        if 'sqlite' in database_url:
            engine = create_async_engine(
                database_url,
                echo=settings.debug,
                # SQLite doesn't support connection pooling parameters
            )
            # Add WAL mode event listener for SQLite
            event.listen(engine.sync_engine, "connect", _enable_wal_mode)
        else:
            # PostgreSQL or other database configuration
            engine = create_async_engine(
                database_url,
                echo=settings.debug,
                pool_size=settings.connection_pool_size,
                max_overflow=10,
                pool_timeout=30,
                pool_recycle=3600,
                pool_pre_ping=True,
            )
    
    return engine


def get_async_session():
    """Get async session factory."""
    global async_session
    if async_session is None:
        async_session = async_sessionmaker(
            get_engine(),
            class_=AsyncSession,
            expire_on_commit=False
        )
    return async_session


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    
    Yields:
        AsyncSession: Database session with proper cleanup
    """
    async_session_factory = get_async_session()
    async with async_session_factory() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_database():
    """
    Initialize database with tables and settings.
    
    This function creates all tables and sets up the database
    with proper WAL mode and performance settings.
    """
    try:
        engine = get_engine()
        
        # Create all tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # Test connection and verify WAL mode
        async with engine.begin() as conn:
            if 'sqlite' in str(engine.url):
                result = await conn.execute(text("PRAGMA journal_mode"))
                mode = result.scalar()
                logger.info(f"Database journal mode: {mode}")
                
                result = await conn.execute(text("PRAGMA foreign_keys"))
                fk_enabled = result.scalar()
                logger.info(f"Foreign keys enabled: {bool(fk_enabled)}")
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_database():
    """Clean up database connections."""
    global engine, async_session
    if engine:
        await engine.dispose()
        engine = None
        async_session = None
        logger.info("Database connections closed")


# Context manager for database transactions
class DatabaseTransaction:
    """Context manager for database transactions with proper error handling."""
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self) -> AsyncSession:
        """Enter context and create session."""
        async_session_factory = get_async_session()
        self.session = async_session_factory()
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit context with proper cleanup."""
        if self.session:
            if exc_type is not None:
                await self.session.rollback()
                logger.error(f"Transaction rolled back due to {exc_type.__name__}: {exc_val}")
            else:
                await self.session.commit()
            await self.session.close()


# Export main components
__all__ = [
    "Base",
    "get_engine", 
    "get_async_session",
    "get_db_session",
    "init_database",
    "close_database",
    "DatabaseTransaction"
]
