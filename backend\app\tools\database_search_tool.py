"""
Database search tool for AI-Powered Dashboard with semantic embeddings.

This tool provides vector similarity search using Ollama embeddings
following research/mirascope/page6-tools.md for tool implementation.
"""

import asyncio
import logging
import json
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator

from mirascope.core.base.tool import BaseTool
from sqlalchemy import select, text, and_, or_, func
from sqlalchemy.orm import selectinload
import httpx

from app.database.connection import get_db_session, DatabaseTransaction
from app.database.models import (
    EmbeddingIndex, Task as TaskModel, Event as EventModel, 
    UserInput as UserInputModel, ProcessingLog
)
from app.models.pydantic_models import SearchResult
from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class HybridSearchInput(BaseModel):
    """Input model for hybrid search operations combining keyword and vector search."""
    query: str = Field(..., description="Search query text")
    search_type: str = Field(default="all", description="Search type: 'tasks', 'events', 'inputs', 'all'")
    search_mode: str = Field(default="hybrid", description="Search mode: 'keyword', 'vector', 'hybrid'")
    limit: int = Field(default=10, description="Maximum number of results")
    similarity_threshold: float = Field(default=0.6, description="Minimum similarity score (0.0-1.0)")
    keyword_weight: float = Field(default=0.4, description="Weight for keyword search results (0.0-1.0)")
    vector_weight: float = Field(default=0.6, description="Weight for vector search results (0.0-1.0)")
    include_metadata: bool = Field(default=True, description="Include item metadata in results")
    date_filter: Optional[str] = Field(None, description="Date filter: 'recent', 'this_week', 'this_month'")
    faceted_filters: Optional[Dict[str, Any]] = Field(None, description="Faceted search filters")
    boost_recent: bool = Field(default=True, description="Boost recent content in ranking")
    context_aware: bool = Field(default=True, description="Use context-aware semantic ranking")

    @validator('similarity_threshold')
    def validate_threshold(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")
        return v

    @validator('keyword_weight', 'vector_weight')
    def validate_weights(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Weights must be between 0.0 and 1.0")
        return v

    @validator('search_type')
    def validate_search_type(cls, v):
        valid_types = ['tasks', 'events', 'inputs', 'all']
        if v not in valid_types:
            raise ValueError(f"Search type must be one of: {valid_types}")
        return v

    @validator('search_mode')
    def validate_search_mode(cls, v):
        valid_modes = ['keyword', 'vector', 'hybrid']
        if v not in valid_modes:
            raise ValueError(f"Search mode must be one of: {valid_modes}")
        return v


class IndexContentInput(BaseModel):
    """Input model for indexing content with embeddings."""
    content_type: str = Field(..., description="Content type: 'task', 'event', 'user_input'")
    content_id: int = Field(..., description="ID of the content item")
    text_content: str = Field(..., description="Text content to embed and index")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    force_reindex: bool = Field(default=False, description="Force reindexing even if already exists")

    @validator('content_type')
    def validate_content_type(cls, v):
        valid_types = ['task', 'event', 'user_input']
        if v not in valid_types:
            raise ValueError(f"Content type must be one of: {valid_types}")
        return v


class HybridDatabaseSearchTool(BaseTool):
    """
    Mirascope tool for hybrid database search using both keyword and vector similarity.
    
    Implements LangSearch Database-inspired hybrid search with Bocha-Semantic-Ranker
    concepts for enhanced accuracy and relevance scoring.
    """
    
    def __init__(self):
        super().__init__()
        # Initialize required configuration from settings - assign to local variables
        self._ollama_endpoint = settings.ollama_base_url
        self._embedding_model = settings.ollama_embedding_model
        self._keep_alive = settings.ollama_keep_alive
    
    async def hybrid_search(self, input_data: HybridSearchInput) -> Dict[str, Any]:
        """
        Perform hybrid search combining keyword and vector search with Bocha-Semantic-Ranker inspired ranking.
        
        Args:
            input_data: Hybrid search parameters including query and mode
            
        Returns:
            Dict[str, Any]: Hybrid search results with enhanced ranking
        """
        try:
            logger.info(f"Performing hybrid search: '{input_data.query}' (mode: {input_data.search_mode})")
            
            # Initialize result containers
            keyword_results = []
            vector_results = []
            
            # Perform keyword search if needed
            if input_data.search_mode in ['keyword', 'hybrid']:
                keyword_results = await self._keyword_search(
                    query=input_data.query,
                    search_type=input_data.search_type,
                    limit=input_data.limit * 2,  # Get more for better ranking
                    date_filter=input_data.date_filter,
                    faceted_filters=input_data.faceted_filters
                )
            
            # Perform vector search if needed
            if input_data.search_mode in ['vector', 'hybrid']:
                query_embedding = await self._generate_embedding(input_data.query)
                if query_embedding:
                    vector_results = await self._vector_search(
                        query_embedding=query_embedding,
                        search_type=input_data.search_type,
                        limit=input_data.limit * 2,  # Get more for better ranking
                        threshold=input_data.similarity_threshold,
                        date_filter=input_data.date_filter,
                        faceted_filters=input_data.faceted_filters
                    )
                elif input_data.search_mode == 'vector':
                    return {
                        "success": False,
                        "error": "Failed to generate query embedding",
                        "message": "Vector search failed - check Ollama connection"
                    }
            
            # Combine and rank results using Bocha-Semantic-Ranker inspired algorithm
            combined_results = await self._bocha_semantic_ranker(
                keyword_results=keyword_results,
                vector_results=vector_results,
                original_query=input_data.query,
                keyword_weight=input_data.keyword_weight,
                vector_weight=input_data.vector_weight,
                boost_recent=input_data.boost_recent,
                context_aware=input_data.context_aware
            )
            
            # Apply final filters and limit
            final_results = combined_results[:input_data.limit]
            
            # Format results with enhanced metadata
            formatted_results = await self._format_hybrid_results(
                final_results, 
                input_data.query,
                input_data.include_metadata,
                input_data.search_mode
            )
            
            logger.info(f"Hybrid search completed: {len(formatted_results)} results")
            
            return {
                "success": True,
                "query": input_data.query,
                "results": formatted_results,
                "count": len(formatted_results),
                "search_metadata": {
                    "search_mode": input_data.search_mode,
                    "search_type": input_data.search_type,
                    "keyword_results_found": len(keyword_results),
                    "vector_results_found": len(vector_results),
                    "similarity_threshold": input_data.similarity_threshold,
                    "keyword_weight": input_data.keyword_weight,
                    "vector_weight": input_data.vector_weight,
                    "embedding_model": self._embedding_model
                },
                "message": f"Found {len(formatted_results)} relevant results using {input_data.search_mode} search"
            }
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Hybrid search failed - check system configuration"
            }
    async def _keyword_search(
        self,
        query: str,
        search_type: str,
        limit: int,
        date_filter: Optional[str],
        faceted_filters: Optional[Dict[str, Any]]
    ) -> List[Tuple[Any, float]]:
        """
        Perform keyword-based search using SQL LIKE and full-text search.
        
        Returns list of (content_item, relevance_score) tuples.
        """
    async def _vector_search(
        self,
        query_embedding: List[float],
        search_type: str,
        limit: int,
        threshold: float,
        date_filter: Optional[str],
        faceted_filters: Optional[Dict[str, Any]]
    ) -> List[Tuple[EmbeddingIndex, float]]:
        """
        Perform vector similarity search using cosine similarity.
        
        Returns list of (embedding_index, similarity_score) tuples.
        """
        try:
            async with DatabaseTransaction() as session:
                # Base query
                query = select(EmbeddingIndex)
                
                # Apply content type filter
                if search_type != "all":
                    query = query.where(EmbeddingIndex.content_type == search_type)
                
                # Apply date filter if specified
                if date_filter:
                    date_condition = self._get_date_condition(EmbeddingIndex, date_filter)
                    if date_condition is not None:
                        query = query.where(date_condition)
                
                # Apply faceted filters
                if faceted_filters:
                    # Apply filters based on metadata
                    for key, value in faceted_filters.items():
                        if key == 'category':
                            query = query.where(EmbeddingIndex.metadata[key].as_string() == value)
                        elif key == 'priority':
                            query = query.where(EmbeddingIndex.metadata[key].as_string() == value)
                        # Add more faceted filters as needed
                
                # Execute query to get all candidates
                result = await session.execute(query)
                candidates = result.scalars().all()
                
                # Calculate similarity scores
                similar_items = []
                for candidate in candidates:
                    if candidate.embedding_vector:
                        similarity = self._cosine_similarity(query_embedding, candidate.embedding_vector)
                        if similarity >= threshold:
                            similar_items.append((candidate, similarity))
                
                # Sort by similarity (descending) and limit
                similar_items.sort(key=lambda x: x[1], reverse=True)
                return similar_items[:limit]
                
        except Exception as e:
            logger.error(f"Error in vector search: {e}")
            return []
    
    async def _bocha_semantic_ranker(
        self,
        keyword_results: List[Tuple[Any, float]],
        vector_results: List[Tuple[Any, float]], 
        original_query: str,
        keyword_weight: float,
        vector_weight: float,
        boost_recent: bool,
        context_aware: bool
    ) -> List[Dict[str, Any]]:
        """
        Bocha-Semantic-Ranker inspired algorithm for combining and ranking search results.
        
        This implements the core concepts from LangSearch's semantic ranking engine:
        - Combines keyword and vector search results
        - Context-aware relevance scoring
        - Recency boosting
        - Intent understanding
        """
        try:
            # Create unified result tracking
            result_map = {}
            
            # Process keyword results
            for item, keyword_score in keyword_results:
                item_id = self._get_item_identifier(item)
                if item_id not in result_map:
                    result_map[item_id] = {
                        'item': item,
                        'item_type': type(item).__name__,
                        'keyword_score': keyword_score,
                        'vector_score': 0.0,
                        'combined_score': 0.0,
                        'boost_factors': {}
                    }
                else:
                    # Update if better keyword score
                    result_map[item_id]['keyword_score'] = max(
                        result_map[item_id]['keyword_score'], 
                        keyword_score
                    )
            
            # Process vector results
            for embedding_index, vector_score in vector_results:
                # Get the original item
                original_item = await self._get_original_item_from_embedding(embedding_index)
                if original_item:
                    item_id = self._get_embedding_item_identifier(embedding_index)
                    if item_id not in result_map:
                        result_map[item_id] = {
                            'item': original_item,
                            'item_type': embedding_index.content_type,
                            'keyword_score': 0.0,
                            'vector_score': vector_score,
                            'combined_score': 0.0,
                            'boost_factors': {},
                            'embedding_index': embedding_index
                        }
                    else:
                        # Update vector score
                        result_map[item_id]['vector_score'] = max(
                            result_map[item_id]['vector_score'], 
                            vector_score
                        )
                        result_map[item_id]['embedding_index'] = embedding_index
            
            # Apply Bocha-Semantic-Ranker scoring
            for item_id, result_data in result_map.items():
                # Base combined score
                combined_score = (
                    result_data['keyword_score'] * keyword_weight + 
                    result_data['vector_score'] * vector_weight
                )
                
                # Context-aware boosting
                if context_aware:
                    context_boost = await self._calculate_context_boost(
                        result_data['item'], 
                        original_query,
                        result_data.get('embedding_index')
                    )
                    combined_score += context_boost
                    result_data['boost_factors']['context'] = context_boost
                
                # Recency boosting
                if boost_recent:
                    recency_boost = self._calculate_recency_boost(result_data['item'])
                    combined_score += recency_boost
                    result_data['boost_factors']['recency'] = recency_boost
                
                # Intent matching boost (if both keyword and vector found the item)
                if result_data['keyword_score'] > 0 and result_data['vector_score'] > 0:
                    intent_boost = 0.1  # 10% boost for items found by both methods
                    combined_score += intent_boost
                    result_data['boost_factors']['intent_alignment'] = intent_boost
                
                # Diversity boost (prefer variety in result types)
                diversity_boost = self._calculate_diversity_boost(
                    result_data['item_type'], 
                    [r['item_type'] for r in result_map.values()]
                )
                combined_score += diversity_boost
                result_data['boost_factors']['diversity'] = diversity_boost
                
                result_data['combined_score'] = min(combined_score, 1.0)  # Cap at 1.0
            
            # Sort by combined score and return
            ranked_results = sorted(
                result_map.values(), 
                key=lambda x: x['combined_score'], 
                reverse=True
            )
            
            return ranked_results
            
        except Exception as e:
            logger.error(f"Error in Bocha-Semantic-Ranker: {e}")
            return []
    
    def _get_item_identifier(self, item: Any) -> str:
        """Get unique identifier for an item."""
        item_type = type(item).__name__
        return f"{item_type}:{item.id}"
    
    def _get_embedding_item_identifier(self, embedding_index: EmbeddingIndex) -> str:
        """Get unique identifier from embedding index."""
        return f"{embedding_index.content_type}:{embedding_index.content_id}"
    
    async def _get_original_item_from_embedding(self, embedding_index: EmbeddingIndex) -> Optional[Any]:
        """Get original item from embedding index."""
        try:
            async with DatabaseTransaction() as session:
                if embedding_index.content_type == "task":
                    result = await session.execute(
                        select(TaskModel).where(TaskModel.id == embedding_index.content_id)
                    )
                    return result.scalar_one_or_none()
                elif embedding_index.content_type == "event":
                    result = await session.execute(
                        select(EventModel).where(EventModel.id == embedding_index.content_id)
                    )
                    return result.scalar_one_or_none()
                elif embedding_index.content_type == "user_input":
                    result = await session.execute(
                        select(UserInputModel).where(UserInputModel.id == embedding_index.content_id)
                    )
                    return result.scalar_one_or_none()
            return None
        except Exception as e:
            logger.error(f"Error getting original item: {e}")
            return None
    
    async def _calculate_context_boost(self, item: Any, query: str, embedding_index: Optional[EmbeddingIndex] = None) -> float:
        """Calculate context-aware boost based on item relevance to query context."""
        try:
            context_boost = 0.0
            
            # Category relevance boost
            if hasattr(item, 'category') and item.category:
                if any(word in query.lower() for word in item.category.lower().split()):
                    context_boost += 0.05
            
            # Priority/urgency context boost
            if hasattr(item, 'priority'):
                urgent_keywords = ['urgent', 'asap', 'immediately', 'critical', 'important']
                if any(keyword in query.lower() for keyword in urgent_keywords):
                    if item.priority in ['urgent', 'high']:
                        context_boost += 0.08
            
            # Time context boost
            if hasattr(item, 'due_date') or hasattr(item, 'start_datetime'):
                time_keywords = ['today', 'tomorrow', 'this week', 'next week', 'soon']
                if any(keyword in query.lower() for keyword in time_keywords):
                    context_boost += 0.06
            
            # Status context boost  
            if hasattr(item, 'status'):
                status_keywords = ['completed', 'done', 'finished', 'pending', 'in progress']
                if any(keyword in query.lower() for keyword in status_keywords):
                    if item.status.lower() in query.lower():
                        context_boost += 0.07
            
            return context_boost
            
        except Exception as e:
            logger.error(f"Error calculating context boost: {e}")
            return 0.0
    
    def _calculate_recency_boost(self, item: Any) -> float:
        """Calculate recency boost - more recent items get higher scores."""
        try:
            now = datetime.now()
            recency_boost = 0.0
            
            # Get creation date
            created_at = None
            if hasattr(item, 'created_at'):
                created_at = item.created_at
            elif hasattr(item, 'start_datetime'):
                created_at = item.start_datetime
            
            if created_at:
                days_ago = (now - created_at).days
                
                if days_ago <= 1:  # Within last day
                    recency_boost = 0.15
                elif days_ago <= 7:  # Within last week
                    recency_boost = 0.10
                elif days_ago <= 30:  # Within last month
                    recency_boost = 0.05
                # Items older than a month get no boost
            
            return recency_boost
            
        except Exception as e:
            logger.error(f"Error calculating recency boost: {e}")
            return 0.0
    
    def _calculate_diversity_boost(self, item_type: str, all_types: List[str]) -> float:
        """Calculate diversity boost to ensure variety in results."""
        try:
            # Count occurrences of this type
            type_count = all_types.count(item_type)
            total_items = len(all_types)
            
            if total_items == 0:
                return 0.0
            
            # Reduce boost for over-represented types
            type_ratio = type_count / total_items
            
            if type_ratio > 0.7:  # If one type dominates > 70%
                return -0.02  # Small penalty
            elif type_ratio < 0.3:  # If type is underrepresented < 30%
                return 0.03  # Small boost for diversity
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating diversity boost: {e}")
            return 0.0
    
    def _get_date_condition(self, model_class: Any, date_filter: str, date_field: str = 'created_at'):
        """Get SQLAlchemy date filter condition."""
        try:
            now = datetime.now()
            date_field_attr = getattr(model_class, date_field)
            
            if date_filter == "recent":
                cutoff = now - timedelta(days=7)
                return date_field_attr >= cutoff
            elif date_filter == "this_week":
                cutoff = now - timedelta(days=7)
                return date_field_attr >= cutoff
            elif date_filter == "this_month":
                cutoff = now - timedelta(days=30)
                return date_field_attr >= cutoff
            
            return None
        except Exception as e:
            logger.error(f"Error creating date condition: {e}")
            return None
    
    def _apply_faceted_filters_tasks(self, faceted_filters: Dict[str, Any]) -> List[Any]:
        """Apply faceted filters for tasks."""
        conditions = []
        try:
            if 'category' in faceted_filters:
                conditions.append(TaskModel.category == faceted_filters['category'])
            if 'priority' in faceted_filters:
                conditions.append(TaskModel.priority == faceted_filters['priority'])
            if 'status' in faceted_filters:
                conditions.append(TaskModel.status == faceted_filters['status'])
            if 'completed' in faceted_filters:
                if faceted_filters['completed']:
                    conditions.append(TaskModel.status == 'completed')
                else:
                    conditions.append(TaskModel.status != 'completed')
        except Exception as e:
            logger.error(f"Error applying task faceted filters: {e}")
        return conditions
    
    def _apply_faceted_filters_events(self, faceted_filters: Dict[str, Any]) -> List[Any]:
        """Apply faceted filters for events."""
        conditions = []
        try:
            if 'event_type' in faceted_filters:
                conditions.append(EventModel.event_type == faceted_filters['event_type'])
            if 'status' in faceted_filters:
                conditions.append(EventModel.status == faceted_filters['status'])
            if 'location' in faceted_filters:
                conditions.append(EventModel.location.ilike(f"%{faceted_filters['location']}%"))
        except Exception as e:
            logger.error(f"Error applying event faceted filters: {e}")
        return conditions
    
    def _apply_faceted_filters_inputs(self, faceted_filters: Dict[str, Any]) -> List[Any]:
        """Apply faceted filters for user inputs."""
        conditions = []
        try:
            if 'category' in faceted_filters:
                conditions.append(UserInputModel.category == faceted_filters['category'])
        except Exception as e:
            logger.error(f"Error applying input faceted filters: {e}")
        return conditions
    
    async def _format_hybrid_results(
        self, 
        results: List[Dict[str, Any]], 
        query: str, 
        include_metadata: bool = True,
        search_mode: str = "hybrid"
    ) -> List[Dict[str, Any]]:
        """Format hybrid search results with enhanced metadata."""
        try:
            formatted_results = []
            
            for result_data in results:
                item = result_data['item']
                item_type = result_data['item_type']
                
                # Base item data
                formatted_item = {
                    "id": item.id,
                    "type": item_type.lower().replace('model', ''),
                    "title": getattr(item, 'title', getattr(item, 'name', 'Untitled')),
                    "content": getattr(item, 'description', getattr(item, 'content', '')),
                    "created_at": getattr(item, 'created_at', None),
                    "relevance_score": result_data['combined_score']
                }
                
                # Type-specific data
                if item_type == 'TaskModel':
                    formatted_item.update({
                        "category": getattr(item, 'category', None),
                        "priority": getattr(item, 'priority', None),
                        "status": getattr(item, 'status', None),
                        "due_date": getattr(item, 'due_date', None),
                        "assignee_id": getattr(item, 'assignee_id', None)
                    })
                elif item_type == 'EventModel':
                    formatted_item.update({
                        "event_type": getattr(item, 'event_type', None),
                        "start_datetime": getattr(item, 'start_datetime', None),
                        "end_datetime": getattr(item, 'end_datetime', None),
                        "location": getattr(item, 'location', None),
                        "attendees": getattr(item, 'attendees', [])
                    })
                elif item_type == 'UserInputModel':
                    formatted_item.update({
                        "input_type": getattr(item, 'input_type', None),
                        "category": getattr(item, 'category', None),
                        "sentiment": getattr(item, 'sentiment', None)
                    })
                
                # Add enhanced metadata if requested
                if include_metadata:
                    formatted_item["search_metadata"] = {
                        "keyword_score": result_data['keyword_score'],
                        "vector_score": result_data['vector_score'],
                        "combined_score": result_data['combined_score'],
                        "boost_factors": result_data['boost_factors'],
                        "search_mode": search_mode,
                        "found_by": []
                    }
                    
                    # Indicate which search methods found this item
                    if result_data['keyword_score'] > 0:
                        formatted_item["search_metadata"]["found_by"].append("keyword")
                    if result_data['vector_score'] > 0:
                        formatted_item["search_metadata"]["found_by"].append("vector")
                
                formatted_results.append(formatted_item)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error formatting hybrid results: {e}")
            return []
    
    async def _keyword_search_tasks(self, session, query: str, limit: int, date_filter: Optional[str], faceted_filters: Optional[Dict[str, Any]]) -> List[Tuple[TaskModel, float]]:
        """Keyword search within tasks."""
        try:
            # Build search terms
            search_terms = query.lower().split()
            search_pattern = f"%{' '.join(search_terms)}%"
            
            # Base query
            query_builder = select(TaskModel)
            conditions = []
            
            # Text search conditions
            text_conditions = []
            for term in search_terms:
                term_pattern = f"%{term}%"
                text_conditions.append(
                    or_(
                        TaskModel.title.ilike(term_pattern),
                        TaskModel.description.ilike(term_pattern),
                        TaskModel.category.ilike(term_pattern)
                    )
                )
            
            if text_conditions:
                conditions.append(and_(*text_conditions))
            
            # Apply date filter
            if date_filter:
                date_condition = self._get_date_condition(TaskModel, date_filter)
                if date_condition is not None:
                    conditions.append(date_condition)
            
            # Apply faceted filters
            if faceted_filters:
                facet_conditions = self._apply_faceted_filters_tasks(faceted_filters)
                conditions.extend(facet_conditions)
            
            # Build final query
            if conditions:
                query_builder = query_builder.where(and_(*conditions))
            
            query_builder = query_builder.limit(limit)
            
            # Execute query
            result = await session.execute(query_builder)
            tasks = result.scalars().all()
            
            # Calculate relevance scores
            scored_results = []
            for task in tasks:
                score = self._calculate_keyword_relevance_score(
                    query, 
                    f"{task.title} {task.description or ''} {task.category or ''}"
                )
                scored_results.append((task, score))
            
            return scored_results
            
        except Exception as e:
            logger.error(f"Error in task keyword search: {e}")
            return []
    
    async def _keyword_search_events(self, session, query: str, limit: int, date_filter: Optional[str], faceted_filters: Optional[Dict[str, Any]]) -> List[Tuple[EventModel, float]]:
        """Keyword search within events."""
        try:
            search_terms = query.lower().split()
            
            query_builder = select(EventModel)
            conditions = []
            
            # Text search conditions
            text_conditions = []
            for term in search_terms:
                term_pattern = f"%{term}%"
                text_conditions.append(
                    or_(
                        EventModel.title.ilike(term_pattern),
                        EventModel.description.ilike(term_pattern),
                        EventModel.location.ilike(term_pattern),
                        EventModel.event_type.ilike(term_pattern)
                    )
                )
            
            if text_conditions:
                conditions.append(and_(*text_conditions))
            
            # Apply filters
            if date_filter:
                date_condition = self._get_date_condition(EventModel, date_filter, date_field='start_datetime')
                if date_condition is not None:
                    conditions.append(date_condition)
            
            if faceted_filters:
                facet_conditions = self._apply_faceted_filters_events(faceted_filters)
                conditions.extend(facet_conditions)
            
            if conditions:
                query_builder = query_builder.where(and_(*conditions))
            
            query_builder = query_builder.limit(limit)
            
            result = await session.execute(query_builder)
            events = result.scalars().all()
            
            # Calculate relevance scores
            scored_results = []
            for event in events:
                score = self._calculate_keyword_relevance_score(
                    query, 
                    f"{event.title} {event.description or ''} {event.location or ''} {event.event_type or ''}"
                )
                scored_results.append((event, score))
            
            return scored_results
            
        except Exception as e:
            logger.error(f"Error in event keyword search: {e}")
            return []
    
    async def _keyword_search_inputs(self, session, query: str, limit: int, date_filter: Optional[str], faceted_filters: Optional[Dict[str, Any]]) -> List[Tuple[UserInputModel, float]]:
        """Keyword search within user inputs."""
        try:
            search_terms = query.lower().split()
            
            query_builder = select(UserInputModel)
            conditions = []
            
            # Text search conditions
            text_conditions = []
            for term in search_terms:
                term_pattern = f"%{term}%"
                text_conditions.append(
                    or_(
                        UserInputModel.input_text.ilike(term_pattern),
                        UserInputModel.category.ilike(term_pattern)
                    )
                )
            
            if text_conditions:
                conditions.append(and_(*text_conditions))
            
            # Apply filters
            if date_filter:
                date_condition = self._get_date_condition(UserInputModel, date_filter)
                if date_condition is not None:
                    conditions.append(date_condition)
            
            if faceted_filters:
                facet_conditions = self._apply_faceted_filters_inputs(faceted_filters)
                conditions.extend(facet_conditions)
            
            if conditions:
                query_builder = query_builder.where(and_(*conditions))
            
            query_builder = query_builder.limit(limit)
            
            result = await session.execute(query_builder)
            inputs = result.scalars().all()
            
            # Calculate relevance scores
            scored_results = []
            for user_input in inputs:
                score = self._calculate_keyword_relevance_score(
                    query, 
                    f"{user_input.input_text} {user_input.category or ''}"
                )
                scored_results.append((user_input, score))
            
            return scored_results
            
        except Exception as e:
            logger.error(f"Error in user input keyword search: {e}")
            return []
    
    def _calculate_keyword_relevance_score(self, query: str, content: str) -> float:
        """
        Calculate keyword relevance score using TF-IDF inspired algorithm.
        
        This implements a simplified version of keyword matching relevance.
        """
        try:
            query_terms = set(query.lower().split())
            content_terms = content.lower().split()
            content_word_count = len(content_terms)
            
            if content_word_count == 0:
                return 0.0
            
            # Term frequency calculation
            term_frequencies = {}
            for term in content_terms:
                term_frequencies[term] = term_frequencies.get(term, 0) + 1
            
            # Calculate score based on query term matches
            total_score = 0.0
            matched_terms = 0
            
            for query_term in query_terms:
                if query_term in term_frequencies:
                    # TF score (normalized by document length)
                    tf_score = term_frequencies[query_term] / content_word_count
                    
                    # Position boost (if term appears in first few words)
                    position_boost = 1.0
                    try:
                        first_occurrence = content_terms.index(query_term)
                        if first_occurrence < 5:  # Boost terms in first 5 positions
                            position_boost = 1.5
                    except ValueError:
                        pass
                    
                    # Exact match boost
                    exact_boost = 1.2 if query_term in content.lower() else 1.0
                    
                    term_score = tf_score * position_boost * exact_boost
                    total_score += term_score
                    matched_terms += 1
            
            # Normalize by query length and add phrase bonus
            if len(query_terms) > 0:
                base_score = total_score / len(query_terms)
                
                # Phrase matching bonus (if consecutive words match)
                phrase_bonus = 0.0
                if len(query_terms) > 1 and query.lower() in content.lower():
                    phrase_bonus = 0.3
                
                # Coverage bonus (how many query terms were matched)
                coverage_bonus = (matched_terms / len(query_terms)) * 0.2
                
                final_score = base_score + phrase_bonus + coverage_bonus
                return min(final_score, 1.0)  # Cap at 1.0
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating keyword relevance: {e}")
            return 0.0
    
    async def index_content(self, input_data: IndexContentInput) -> Dict[str, Any]:
        """
        Index content with embeddings for semantic search.
        
        Args:
            input_data: Content indexing parameters
            
        Returns:
            Dict[str, Any]: Indexing result status
        """
        try:
            logger.info(f"Indexing {input_data.content_type} content ID: {input_data.content_id}")
            
            async with DatabaseTransaction() as session:
                # Check if already indexed
                if not input_data.force_reindex:
                    existing = await session.execute(
                        select(EmbeddingIndex).where(
                            and_(
                                EmbeddingIndex.content_type == input_data.content_type,
                                EmbeddingIndex.content_id == input_data.content_id
                            )
                        )
                    )
                    if existing.scalar_one_or_none():
                        return {
                            "success": True,
                            "message": "Content already indexed (use force_reindex=True to reindex)",
                            "skipped": True
                        }
                
                # Generate embedding
                embedding = await self._generate_embedding(input_data.text_content)
                if not embedding:
                    return {
                        "success": False,
                        "error": "Failed to generate embedding for content"
                    }
                
                # Create or update embedding index
                existing_result = await session.execute(
                    select(EmbeddingIndex).where(
                        and_(
                            EmbeddingIndex.content_type == input_data.content_type,
                            EmbeddingIndex.content_id == input_data.content_id
                        )
                    )
                )
                existing_index = existing_result.scalar_one_or_none()
                
                if existing_index:
                    # Update existing
                    existing_index.embedding_vector = embedding
                    existing_index.text_content = input_data.text_content
                    existing_index.metadata = input_data.metadata or {}
                    existing_index.updated_at = datetime.now()
                    index_record = existing_index
                else:
                    # Create new
                    index_record = EmbeddingIndex(
                        content_type=input_data.content_type,
                        content_id=input_data.content_id,
                        embedding_vector=embedding,
                        text_content=input_data.text_content,
                        metadata=input_data.metadata or {},
                        model_name=self._embedding_model
                    )
                    session.add(index_record)
                
                await session.flush()
                
                logger.info(f"Content indexed successfully: {input_data.content_type}:{input_data.content_id}")
                
                return {
                    "success": True,
                    "content_type": input_data.content_type,
                    "content_id": input_data.content_id,
                    "embedding_dimensions": len(embedding),
                    "model_used": self._embedding_model,
                    "message": "Content indexed successfully"
                }
                
        except Exception as e:
            logger.error(f"Error indexing content: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to index content"
            }
    
    async def bulk_index(self, content_items: List[IndexContentInput]) -> Dict[str, Any]:
        """
        Bulk index multiple content items efficiently.
        
        Args:
            content_items: List of content items to index
            
        Returns:
            Dict[str, Any]: Bulk indexing results
        """
        try:
            logger.info(f"Starting bulk indexing of {len(content_items)} items")
            
            results = {
                "indexed": 0,
                "skipped": 0,
                "failed": 0,
                "errors": []
            }
            
            # Process in batches to avoid overwhelming Ollama
            batch_size = 5
            batches = [content_items[i:i + batch_size] for i in range(0, len(content_items), batch_size)]
            
            for batch in batches:
                # Generate embeddings for batch
                batch_texts = [item.text_content for item in batch]
                batch_embeddings = await self._generate_embeddings_batch(batch_texts)
                
                if len(batch_embeddings) != len(batch_texts):
                    logger.warning(f"Embedding count mismatch in batch: {len(batch_embeddings)} != {len(batch_texts)}")
                    continue
                
                # Index each item in the batch
                async with DatabaseTransaction() as session:
                    for item, embedding in zip(batch, batch_embeddings):
                        try:
                            if not embedding:
                                results["failed"] += 1
                                results["errors"].append(f"{item.content_type}:{item.content_id} - embedding failed")
                                continue
                            
                            # Check if exists (if not forcing reindex)
                            if not item.force_reindex:
                                existing = await session.execute(
                                    select(EmbeddingIndex).where(
                                        and_(
                                            EmbeddingIndex.content_type == item.content_type,
                                            EmbeddingIndex.content_id == item.content_id
                                        )
                                    )
                                )
                                if existing.scalar_one_or_none():
                                    results["skipped"] += 1
                                    continue
                            
                            # Create or update index record
                            index_record = EmbeddingIndex(
                                content_type=item.content_type,
                                content_id=item.content_id,
                                embedding_vector=embedding,
                                text_content=item.text_content,
                                metadata=item.metadata or {},
                                model_name=self._embedding_model
                            )
                            
                            # Handle update case
                            existing_result = await session.execute(
                                select(EmbeddingIndex).where(
                                    and_(
                                        EmbeddingIndex.content_type == item.content_type,
                                        EmbeddingIndex.content_id == item.content_id
                                    )
                                )
                            )
                            existing_record = existing_result.scalar_one_or_none()
                            
                            if existing_record:
                                existing_record.embedding_vector = embedding
                                existing_record.text_content = item.text_content
                                existing_record.metadata = item.metadata or {}
                                existing_record.updated_at = datetime.now()
                            else:
                                session.add(index_record)
                            
                            results["indexed"] += 1
                            
                        except Exception as e:
                            results["failed"] += 1
                            results["errors"].append(f"{item.content_type}:{item.content_id} - {str(e)}")
                
                # Small delay between batches to be gentle with Ollama
                await asyncio.sleep(0.5)
            
            logger.info(f"Bulk indexing completed: {results['indexed']} indexed, {results['skipped']} skipped, {results['failed']} failed")
            
            return {
                "success": True,
                "total_items": len(content_items),
                "results": results,
                "message": f"Bulk indexing completed: {results['indexed']} items indexed"
            }
            
        except Exception as e:
            logger.error(f"Error in bulk indexing: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Bulk indexing failed"
            }
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the embedding index.
        
        Returns:
            Dict[str, Any]: Index statistics and health information
        """
        try:
            async with DatabaseTransaction() as session:
                # Count by content type
                type_counts = await session.execute(
                    select(EmbeddingIndex.content_type, func.count(EmbeddingIndex.id))
                    .group_by(EmbeddingIndex.content_type)
                )
                type_distribution = dict(type_counts.fetchall())
                
                # Total count
                total_result = await session.execute(
                    select(func.count(EmbeddingIndex.id))
                )
                total_indexed = total_result.scalar() or 0
                
                # Recent indexing activity (last 24 hours)
                yesterday = datetime.now() - timedelta(days=1)
                recent_result = await session.execute(
                    select(func.count(EmbeddingIndex.id))
                    .where(EmbeddingIndex.created_at >= yesterday)
                )
                recent_indexed = recent_result.scalar() or 0
                
                # Model distribution
                model_result = await session.execute(
                    select(EmbeddingIndex.model_name, func.count(EmbeddingIndex.id))
                    .group_by(EmbeddingIndex.model_name)
                )
                model_distribution = dict(model_result.fetchall())
                
                # Check Ollama connectivity
                ollama_status = await self._check_ollama_health()
                
                return {
                    "success": True,
                    "index_stats": {
                        "total_indexed": total_indexed,
                        "recent_activity_24h": recent_indexed,
                        "content_type_distribution": type_distribution,
                        "model_distribution": model_distribution,
                        "current_model": self._embedding_model
                    },
                    "ollama_status": ollama_status,
                    "message": f"Index contains {total_indexed} embedded items"
                }
                
        except Exception as e:
            logger.error(f"Error getting index stats: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to get index statistics"
            }
    
    async def _generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate embedding for a single text using Ollama."""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self._ollama_endpoint}/api/embeddings",
                    json={
                        "model": self._embedding_model,
                        "prompt": text
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("embedding")
                else:
                    logger.error(f"Ollama embedding request failed: {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None
    
    async def _generate_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """Generate embeddings for multiple texts."""
        embeddings = []
        
        # Process sequentially to avoid overwhelming Ollama
        for text in texts:
            embedding = await self._generate_embedding(text)
            embeddings.append(embedding)
            # Small delay between requests
            await asyncio.sleep(0.1)
        
        return embeddings
    
    async def _search_similar_content(
        self,
        query_embedding: List[float],
        search_type: str,
        limit: int,
        threshold: float,
        date_filter: Optional[str],
        include_metadata: bool
    ) -> List[Tuple[EmbeddingIndex, float]]:
        """Search for similar content using cosine similarity."""
        try:
            async with DatabaseTransaction() as session:
                # Base query
                query = select(EmbeddingIndex)
                
                # Apply content type filter
                if search_type != "all":
                    query = query.where(EmbeddingIndex.content_type == search_type)
                
                # Apply date filter if specified
                if date_filter:
                    now = datetime.now()
                    if date_filter == "recent":
                        cutoff = now - timedelta(days=7)
                    elif date_filter == "this_week":
                        cutoff = now - timedelta(days=7)
                    elif date_filter == "this_month":
                        cutoff = now - timedelta(days=30)
                    else:
                        cutoff = None
                    
                    if cutoff:
                        query = query.where(EmbeddingIndex.created_at >= cutoff)
                
                # Execute query to get all candidates
                result = await session.execute(query)
                candidates = result.scalars().all()
                
                # Calculate similarity scores
                similar_items = []
                for candidate in candidates:
                    if candidate.embedding_vector:
                        similarity = self._cosine_similarity(query_embedding, candidate.embedding_vector)
                        if similarity >= threshold:
                            similar_items.append((candidate, similarity))
                
                # Sort by similarity (descending) and limit
                similar_items.sort(key=lambda x: x[1], reverse=True)
                return similar_items[:limit]
                
        except Exception as e:
            logger.error(f"Error searching similar content: {e}")
            return []
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        try:
            # Convert to numpy arrays for efficient computation
            a = np.array(vec1)
            b = np.array(vec2)
            
            # Calculate cosine similarity
            dot_product = np.dot(a, b)
            norm_a = np.linalg.norm(a)
            norm_b = np.linalg.norm(b)
            
            if norm_a == 0 or norm_b == 0:
                return 0.0
            
            return dot_product / (norm_a * norm_b)
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
    
    async def _format_search_results(
        self, 
        search_results: List[Tuple[EmbeddingIndex, float]], 
        original_query: str,
        include_metadata: bool
    ) -> List[Dict[str, Any]]:
        """Format search results with additional context."""
        formatted_results = []
        
        for embedding_index, similarity_score in search_results:
            try:
                # Get original content details
                content_details = await self._get_content_details(
                    embedding_index.content_type,
                    embedding_index.content_id
                )
                
                result = {
                    "similarity_score": round(similarity_score, 4),
                    "content_type": embedding_index.content_type,
                    "content_id": embedding_index.content_id,
                    "text_content": embedding_index.text_content,
                    "indexed_at": embedding_index.created_at.isoformat()
                }
                
                if include_metadata:
                    result["metadata"] = embedding_index.metadata
                    result["content_details"] = content_details
                
                formatted_results.append(result)
                
            except Exception as e:
                logger.warning(f"Error formatting result for {embedding_index.content_type}:{embedding_index.content_id}: {e}")
                continue
        
        return formatted_results
    
    async def _get_content_details(self, content_type: str, content_id: int) -> Dict[str, Any]:
        """Get additional details about the original content."""
        try:
            async with DatabaseTransaction() as session:
                if content_type == "task":
                    result = await session.execute(
                        select(TaskModel).where(TaskModel.id == content_id)
                    )
                    task = result.scalar_one_or_none()
                    if task:
                        return {
                            "title": task.title,
                            "category": task.category,
                            "priority": task.priority,
                            "status": task.status,
                            "due_date": task.due_date.isoformat() if task.due_date else None,
                            "created_at": task.created_at.isoformat()
                        }
                        
                elif content_type == "event":
                    result = await session.execute(
                        select(EventModel).where(EventModel.id == content_id)
                    )
                    event = result.scalar_one_or_none()
                    if event:
                        return {
                            "title": event.title,
                            "start_datetime": event.start_datetime.isoformat(),
                            "end_datetime": event.end_datetime.isoformat() if event.end_datetime else None,
                            "location": event.location,
                            "event_type": event.event_type,
                            "status": event.status
                        }
                        
                elif content_type == "user_input":
                    result = await session.execute(
                        select(UserInputModel).where(UserInputModel.id == content_id)
                    )
                    user_input = result.scalar_one_or_none()
                    if user_input:
                        return {
                            "original_input": user_input.input_text,
                            "category": user_input.category,
                            "ai_confidence": user_input.ai_confidence,
                            "processed_at": user_input.created_at.isoformat()
                        }
                
                return {}
                
        except Exception as e:
            logger.error(f"Error getting content details: {e}")
            return {}
    
    async def _check_ollama_health(self) -> Dict[str, Any]:
        """Check Ollama service health and model availability."""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Check if Ollama is running
                response = await client.get(f"{self._ollama_endpoint}/api/tags")
                
                if response.status_code == 200:
                    models = response.json()
                    available_models = [model["name"] for model in models.get("models", [])]
                    
                    return {
                        "status": "healthy",
                        "endpoint": self._ollama_endpoint,
                        "embedding_model_available": self._embedding_model in available_models,
                        "available_models": available_models
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "endpoint": self._ollama_endpoint,
                        "error": f"HTTP {response.status_code}"
                    }
                    
        except Exception as e:
            return {
                "status": "disconnected",
                "endpoint": self._ollama_endpoint,
                "error": str(e)
            }
    
    async def call(self, operation: str, **kwargs) -> Dict[str, Any]:
        """
        Main tool call method for Mirascope integration.
        
        Args:
            operation: Operation to perform
            **kwargs: Operation-specific parameters
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if operation == "search":
                input_data = HybridSearchInput(**kwargs)
                return await self.hybrid_search(input_data)
            
            elif operation == "index":
                input_data = IndexContentInput(**kwargs)
                return await self.index_content(input_data)
            
            elif operation == "bulk_index":
                content_items_data = kwargs.get("content_items", [])
                content_items = [IndexContentInput(**item) for item in content_items_data]
                return await self.bulk_index(content_items)
            
            elif operation == "stats":
                return await self.get_index_stats()
            
            else:
                return {
                    "success": False,
                    "error": f"Unknown operation: {operation}",
                    "available_operations": ["search", "index", "bulk_index", "stats"]
                }
                
        except Exception as e:
            logger.error(f"Error in database search tool call: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Database search tool error: {str(e)}"
            }


# Global database search tool instance
_database_search_tool = None


def get_database_search_tool() -> HybridDatabaseSearchTool:
    """Get the global database search tool instance (singleton pattern)."""
    global _database_search_tool
    if _database_search_tool is None:
        _database_search_tool = HybridDatabaseSearchTool()
    return _database_search_tool


# Export main components
__all__ = [
    "HybridDatabaseSearchTool",
    "HybridSearchInput",
    "IndexContentInput", 
    "get_database_search_tool"
]
