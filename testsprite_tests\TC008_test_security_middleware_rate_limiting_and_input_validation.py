import requests
import time

BASE_URL = "http://localhost:3000"
HEADERS = {"Content-Type": "application/json"}
TIMEOUT = 30

def test_security_middleware_rate_limiting_and_input_validation():
    """
    Verify that the security middleware correctly enforces rate limiting and input validation
    on all API endpoints to prevent abuse and injection attacks.
    """

    # Define a list of endpoints and methods to test rate limiting and input validation
    # Based on PRD, main API endpoints likely include:
    # - /api/orchestrator (POST) for AI input processing
    # - /api/tasks (POST, GET)
    # - /api/events (POST, GET)
    # - /api/search (POST)
    # - /api/settings (GET, PUT)
    # We'll test a subset representative endpoints for rate limiting and input validation.

    endpoints = [
        {"method": "POST", "url": f"{BASE_URL}/api/orchestrator", "payload": {"input_text": "Test input for AI categorization"}},
        {"method": "POST", "url": f"{BASE_URL}/api/tasks", "payload": {"title": "Test Task", "description": "Task description", "priority": "medium"}},
        {"method": "POST", "url": f"{BASE_URL}/api/events", "payload": {"title": "Test Event", "start_time": "2025-08-01T10:00:00Z", "end_time": "2025-08-01T11:00:00Z"}},
        {"method": "POST", "url": f"{BASE_URL}/api/search", "payload": {"query": "What is AI?"}},
        {"method": "PUT", "url": f"{BASE_URL}/api/settings", "payload": {"api_key": "validapikey123", "model": "gpt-4"}},
    ]

    # 1. Test input validation with malicious payloads (SQL injection, script injection)
    malicious_payloads = [
        {"input_text": "'; DROP TABLE users; --"},
        {"title": "<script>alert('xss')</script>", "description": "desc", "priority": "high"},
        {"title": "Event", "start_time": "2025-08-01T10:00:00Z", "end_time": "2025-08-01T11:00:00Z<script>"},
        {"query": "' OR '1'='1"},
        {"api_key": "validapikey123", "model": "<img src=x onerror=alert(1)>"}
    ]

    for i, endpoint in enumerate(endpoints):
        method = endpoint["method"]
        url = endpoint["url"]
        # Test with malicious payload
        payload = malicious_payloads[i]
        try:
            if method == "POST":
                resp = requests.post(url, json=payload, headers=HEADERS, timeout=TIMEOUT)
            elif method == "PUT":
                resp = requests.put(url, json=payload, headers=HEADERS, timeout=TIMEOUT)
            else:
                continue  # Skip unsupported methods here

            # Expecting 4xx client error due to input validation failure
            assert resp.status_code >= 400 and resp.status_code < 500, \
                f"Input validation failed to reject malicious payload on {url}, status code: {resp.status_code}"
        except requests.RequestException as e:
            assert False, f"Request to {url} failed with exception: {e}"

    # 2. Test rate limiting by sending rapid repeated requests to one endpoint
    rate_limit_url = f"{BASE_URL}/api/orchestrator"
    valid_payload = {"input_text": "Normal input for rate limit test"}

    # Send requests rapidly more than typical rate limit threshold (assumed 5 requests per second)
    success_responses = 0
    rate_limited_responses = 0
    total_requests = 10

    for _ in range(total_requests):
        try:
            resp = requests.post(rate_limit_url, json=valid_payload, headers=HEADERS, timeout=TIMEOUT)
            if resp.status_code == 429:
                rate_limited_responses += 1
            elif 200 <= resp.status_code < 300:
                success_responses += 1
            else:
                # Unexpected status code
                assert False, f"Unexpected status code {resp.status_code} during rate limit test"
        except requests.RequestException as e:
            assert False, f"Request failed during rate limit test with exception: {e}"
        time.sleep(0.1)  # 100ms between requests to trigger rate limiting if threshold is low

    assert rate_limited_responses > 0, "Rate limiting not enforced; no 429 responses received"
    assert success_responses > 0, "No successful requests received before rate limiting triggered"

test_security_middleware_rate_limiting_and_input_validation()