import requests
import time

BASE_URL = "http://localhost:3000"
TIMEOUT = 30
HEADERS = {"Content-Type": "application/json"}

def test_security_middleware_rate_limiting_and_input_validation():
    # Endpoint to test - using AI Orchestrator input endpoint as representative for all endpoints
    endpoint = f"{BASE_URL}/api/orchestrator/input"
    valid_payload = {"text": "Schedule a meeting tomorrow at 10am"}
    injection_payload = {"text": "'; DROP TABLE users; --"}
    invalid_payload = {"text": ""}  # empty input to test validation

    # 1. Test input validation: sending invalid payload (empty text)
    response = requests.post(endpoint, json=invalid_payload, headers=HEADERS, timeout=TIMEOUT)
    assert response.status_code == 400 or response.status_code == 422, \
        f"Expected 400 or 422 for invalid input, got {response.status_code}"
    assert "error" in response.json() or "detail" in response.json(), "Expected error message for invalid input"

    # 2. Test input validation: sending injection payload
    response = requests.post(endpoint, json=injection_payload, headers=HEADERS, timeout=TIMEOUT)
    # The system should sanitize or reject injection attempts
    assert response.status_code in (200, 400, 422), f"Unexpected status code for injection payload: {response.status_code}"
    # If accepted, ensure no harmful effect by checking response structure
    if response.status_code == 200:
        json_resp = response.json()
        assert "category" in json_resp or "result" in json_resp, "Expected valid response structure even for injection input"

    # 3. Test rate limiting: send multiple rapid requests to trigger rate limit
    max_requests = 10
    success_responses = 0
    rate_limited = False
    for i in range(max_requests + 5):
        resp = requests.post(endpoint, json=valid_payload, headers=HEADERS, timeout=TIMEOUT)
        if resp.status_code == 429:
            rate_limited = True
            break
        elif resp.status_code == 200:
            success_responses += 1
        else:
            # Unexpected status code
            assert False, f"Unexpected status code during rate limit test: {resp.status_code}"

    assert rate_limited, "Rate limiting was not enforced after multiple rapid requests"
    assert success_responses > 0, "No successful requests before rate limiting triggered"

    # 4. After waiting, rate limit should reset and allow requests again
    time.sleep(2)  # wait 2 seconds (adjust if needed based on rate limit window)
    resp = requests.post(endpoint, json=valid_payload, headers=HEADERS, timeout=TIMEOUT)
    assert resp.status_code == 200, "Expected successful request after rate limit reset"

test_security_middleware_rate_limiting_and_input_validation()