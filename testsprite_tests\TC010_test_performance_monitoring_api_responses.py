import requests
import time

BASE_URL = "http://localhost:3000"
TIMEOUT = 30

def test_performance_monitoring_api_responses():
    """
    Validate that performance monitoring tools track API response times and resource usage accurately under typical workloads.
    This test simulates typical workloads by making multiple requests to key API endpoints and measures response times.
    It asserts that response times are within acceptable thresholds.
    """

    # Define acceptable max response time in seconds for typical workloads
    MAX_RESPONSE_TIME = 2.0

    created_task_id = None
    created_event_id = None

    try:
        # Test orchestrator input processing
        r = requests.post(
            f"{BASE_URL}/api/orchestrator/process",
            json={"input_text": "Schedule meeting with team tomorrow at 10am"},
            timeout=TIMEOUT,
        )
        assert r.status_code == 200, f"Unexpected status code: {r.status_code}"
        data = r.json()
        assert "category" in data, "Response JSON missing 'category' field"
        assert "confidence" in data, "Response JSON missing 'confidence' field"
        confidence_value = data.get("confidence")
        assert isinstance(confidence_value, (float, int)), "Confidence must be a number"
        assert confidence_value >= 0.7, f"Confidence too low: {confidence_value}"

        # Create a task to test CRUD and performance
        start = time.perf_counter()
        r = requests.post(
            f"{BASE_URL}/api/tasks",
            json={"title": "Performance Test Task", "description": "Task for performance monitoring", "priority": "medium"},
            timeout=TIMEOUT,
        )
        elapsed = time.perf_counter() - start
        assert r.status_code == 201, f"Task creation failed with status: {r.status_code}"
        created_task = r.json()
        created_task_id = created_task.get("id")
        assert created_task_id is not None, "Created task missing 'id'"
        assert elapsed <= MAX_RESPONSE_TIME, f"Task creation took too long: {elapsed}s"

        # Read tasks list
        start = time.perf_counter()
        r = requests.get(f"{BASE_URL}/api/tasks", timeout=TIMEOUT)
        elapsed = time.perf_counter() - start
        assert r.status_code == 200, f"Fetching tasks failed with status: {r.status_code}"
        tasks = r.json()
        assert isinstance(tasks, list), "Tasks response is not a list"
        assert elapsed <= MAX_RESPONSE_TIME, f"Fetching tasks took too long: {elapsed}s"

        # Create a calendar event
        start = time.perf_counter()
        r = requests.post(
            f"{BASE_URL}/api/events",
            json={
                "title": "Performance Test Event",
                "start_time": "2025-08-01T09:00:00Z",
                "end_time": "2025-08-01T10:00:00Z",
            },
            timeout=TIMEOUT,
        )
        elapsed = time.perf_counter() - start
        assert r.status_code == 201, f"Event creation failed with status: {r.status_code}"
        created_event = r.json()
        created_event_id = created_event.get("id")
        assert created_event_id is not None, "Created event missing 'id'"
        assert elapsed <= MAX_RESPONSE_TIME, f"Event creation took too long: {elapsed}s"

        # Read events list
        start = time.perf_counter()
        r = requests.get(f"{BASE_URL}/api/events", timeout=TIMEOUT)
        elapsed = time.perf_counter() - start
        assert r.status_code == 200, f"Fetching events failed with status: {r.status_code}"
        events = r.json()
        assert isinstance(events, list), "Events response is not a list"
        assert elapsed <= MAX_RESPONSE_TIME, f"Fetching events took too long: {elapsed}s"

        # Search agent query
        start = time.perf_counter()
        r = requests.post(
            f"{BASE_URL}/api/search",
            json={"query": "What is the weather today?"},
            timeout=TIMEOUT,
        )
        elapsed = time.perf_counter() - start
        assert r.status_code == 200, f"Search query failed with status: {r.status_code}"
        search_results = r.json()
        assert "results" in search_results, "Search results missing 'results' field"
        assert elapsed <= MAX_RESPONSE_TIME, f"Search query took too long: {elapsed}s"

        # Get settings
        start = time.perf_counter()
        r = requests.get(f"{BASE_URL}/api/settings", timeout=TIMEOUT)
        elapsed = time.perf_counter() - start
        assert r.status_code == 200, f"Fetching settings failed with status: {r.status_code}"
        settings = r.json()
        assert isinstance(settings, dict), "Settings response is not a dict"
        assert elapsed <= MAX_RESPONSE_TIME, f"Fetching settings took too long: {elapsed}s"

        # Update settings
        start = time.perf_counter()
        r = requests.put(
            f"{BASE_URL}/api/settings",
            json={"api_key": "testkey123", "model": "gpt-4", "embedding_index_status": "ready"},
            timeout=TIMEOUT,
        )
        elapsed = time.perf_counter() - start
        assert r.status_code in (200, 204), f"Updating settings failed with status: {r.status_code}"
        assert elapsed <= MAX_RESPONSE_TIME, f"Updating settings took too long: {elapsed}s"

    finally:
        # Cleanup created task
        if created_task_id:
            try:
                requests.delete(f"{BASE_URL}/api/tasks/{created_task_id}", timeout=TIMEOUT)
            except Exception:
                pass
        # Cleanup created event
        if created_event_id:
            try:
                requests.delete(f"{BASE_URL}/api/events/{created_event_id}", timeout=TIMEOUT)
            except Exception:
                pass

test_performance_monitoring_api_responses()
