import requests
import time

BASE_URL = "http://localhost:3000"
TIMEOUT = 30

def test_performance_monitoring_api_responses():
    endpoints_to_test = [
        {"method": "GET", "url": f"{BASE_URL}/api/tasks"},
        {"method": "POST", "url": f"{BASE_URL}/api/tasks", "json": {"title": "Performance Test Task", "description": "Task created for performance monitoring test", "priority": "medium"}},
        {"method": "GET", "url": f"{BASE_URL}/api/events"},
        {"method": "POST", "url": f"{BASE_URL}/api/events", "json": {"title": "Performance Test Event", "start_time": "2025-08-01T10:00:00Z", "end_time": "2025-08-01T11:00:00Z"}},
        {"method": "POST", "url": f"{BASE_URL}/api/ai/orchestrate", "json": {"input": "Schedule a meeting tomorrow at 3pm"}},
        {"method": "GET", "url": f"{BASE_URL}/api/settings"},
        {"method": "PUT", "url": f"{BASE_URL}/api/settings", "json": {"api_key": "testkey", "model": "test-model"}},
        {"method": "GET", "url": f"{BASE_URL}/api/search?q=AI performance"},
    ]

    created_resources = []

    try:
        for ep in endpoints_to_test:
            method = ep["method"]
            url = ep["url"]
            json_payload = ep.get("json", None)

            start_time = time.perf_counter()
            try:
                if method == "GET":
                    response = requests.get(url, timeout=TIMEOUT)
                elif method == "POST":
                    response = requests.post(url, json=json_payload, timeout=TIMEOUT)
                elif method == "PUT":
                    response = requests.put(url, json=json_payload, timeout=TIMEOUT)
                elif method == "DELETE":
                    response = requests.delete(url, timeout=TIMEOUT)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
            except requests.RequestException as e:
                assert False, f"Request to {url} failed with exception: {e}"
            end_time = time.perf_counter()

            elapsed_ms = (end_time - start_time) * 1000

            # Assert response status code is 2xx
            assert 200 <= response.status_code < 300, f"Unexpected status code {response.status_code} for {method} {url}"

            # Assert response time is reasonable (e.g., under 2000 ms)
            assert elapsed_ms < 2000, f"API response time too high: {elapsed_ms:.2f} ms for {method} {url}"

            # Optionally check resource usage headers if provided (e.g., X-Resource-Usage)
            resource_usage = response.headers.get("X-Resource-Usage")
            if resource_usage:
                # Example: header might be "cpu=5%,mem=10MB"
                parts = resource_usage.split(",")
                usage_dict = {}
                for part in parts:
                    if "=" in part:
                        k, v = part.split("=", 1)
                        usage_dict[k.strip()] = v.strip()
                # Basic sanity checks
                if "cpu" in usage_dict:
                    cpu_val = usage_dict["cpu"].rstrip("%")
                    try:
                        cpu_float = float(cpu_val)
                        assert 0 <= cpu_float <= 100, f"CPU usage out of range: {cpu_float}%"
                    except ValueError:
                        assert False, f"Invalid CPU usage value: {usage_dict['cpu']}"
                if "mem" in usage_dict:
                    mem_val = usage_dict["mem"].upper()
                    # Just check it ends with MB or GB
                    assert mem_val.endswith("MB") or mem_val.endswith("GB"), f"Memory usage format unexpected: {mem_val}"

            # Track created resource IDs for cleanup
            if method == "POST" and response.headers.get("Content-Type", "").startswith("application/json"):
                try:
                    data = response.json()
                    if "id" in data:
                        created_resources.append({"url": url, "id": data["id"]})
                except Exception:
                    pass

    finally:
        # Cleanup created resources
        for resource in created_resources:
            try:
                del_url = resource["url"].rsplit("/", 1)[0] + f"/{resource['id']}"
                resp = requests.delete(del_url, timeout=TIMEOUT)
                assert 200 <= resp.status_code < 300, f"Failed to delete resource at {del_url}"
            except Exception:
                pass


test_performance_monitoring_api_responses()
